{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// Format numbers for display\nexport function formatNumber(value: number, decimals: number = 2): string {\n  return new Intl.NumberFormat('en-US', {\n    minimumFractionDigits: decimals,\n    maximumFractionDigits: decimals,\n  }).format(value);\n}\n\n// Format currency\nexport function formatCurrency(value: number, currency: string = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: currency,\n  }).format(value);\n}\n\n// Format percentage\nexport function formatPercentage(value: number, decimals: number = 1): string {\n  return `${formatNumber(value * 100, decimals)}%`;\n}\n\n// Get algorithm icon based on type\nexport function getAlgorithmIcon(algorithmType: string): string {\n  const iconMap: { [key: string]: string } = {\n    'time_series_analyzer': '📈',\n    'forecasting_engine': '🔮',\n    'financial_ratios': '💰',\n    'revenue_agent': '🤖',\n    'scenario_agent': '🎯',\n    'consolidation_agent': '📊',\n    'capex_agent': '🏗️',\n    'web_research_agent': '🔍',\n    'macro_data_agent': '🌍',\n  };\n  \n  return iconMap[algorithmType] || '⚙️';\n}\n\n// Get algorithm color based on category\nexport function getAlgorithmColor(category: string): string {\n  const colorMap: { [key: string]: string } = {\n    'econometric_models': 'bg-blue-100 text-blue-800 border-blue-200',\n    'financial_analysis': 'bg-green-100 text-green-800 border-green-200',\n    'ai_agents': 'bg-purple-100 text-purple-800 border-purple-200',\n    'risk_analysis': 'bg-red-100 text-red-800 border-red-200',\n    'bayesian_models': 'bg-indigo-100 text-indigo-800 border-indigo-200',\n  };\n  \n  return colorMap[category] || 'bg-gray-100 text-gray-800 border-gray-200';\n}\n\n// Debounce function for search\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n// Generate sample data for demos\nexport function generateSampleTimeSeriesData(points: number = 24): number[] {\n  const data: number[] = [];\n  let value = 1000000; // Start with 1M\n  \n  for (let i = 0; i < points; i++) {\n    // Add trend + seasonality + noise\n    const trend = i * 50000; // Growing trend\n    const seasonality = Math.sin((i / 12) * 2 * Math.PI) * 100000; // Annual cycle\n    const noise = (Math.random() - 0.5) * 200000; // Random variation\n    \n    value = 1000000 + trend + seasonality + noise;\n    data.push(Math.max(0, value)); // Ensure non-negative\n  }\n  \n  return data;\n}\n\n// Generate sample financial data\nexport function generateSampleFinancialData() {\n  return {\n    revenue: 50000000,\n    cost_of_goods_sold: 30000000,\n    operating_expenses: 15000000,\n    total_assets: 100000000,\n    total_liabilities: 60000000,\n    equity: 40000000,\n    cash: 10000000,\n    debt: 25000000,\n    customers: 1000000,\n    arpu: 50,\n    churn_rate: 0.05,\n  };\n}\n\n// Validate data for algorithms\nexport function validateTimeSeriesData(data: any[]): { valid: boolean; message: string } {\n  if (!Array.isArray(data)) {\n    return { valid: false, message: 'Data must be an array' };\n  }\n  \n  if (data.length < 6) {\n    return { valid: false, message: 'Need at least 6 data points for analysis' };\n  }\n  \n  const numericData = data.filter(d => typeof d === 'number' && !isNaN(d));\n  if (numericData.length < data.length * 0.8) {\n    return { valid: false, message: 'At least 80% of data points must be numeric' };\n  }\n  \n  return { valid: true, message: 'Data is valid' };\n}\n\n// Extract business insights from analysis results\nexport function extractBusinessInsights(analysisResult: any): string[] {\n  const insights: string[] = [];\n  \n  if (analysisResult?.business_insights) {\n    analysisResult.business_insights.forEach((insight: any) => {\n      if (insight.finding) {\n        insights.push(insight.finding);\n      }\n    });\n  }\n  \n  if (analysisResult?.summary?.key_finding) {\n    insights.push(analysisResult.summary.key_finding);\n  }\n  \n  if (analysisResult?.recommendations) {\n    analysisResult.recommendations.forEach((rec: any) => {\n      if (typeof rec === 'string') {\n        insights.push(rec);\n      } else if (rec.recommendation) {\n        insights.push(rec.recommendation);\n      }\n    });\n  }\n  \n  return insights;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,aAAa,KAAa;QAAE,WAAA,iEAAmB;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAGO,SAAS,eAAe,KAAa;QAAE,WAAA,iEAAmB;IAC/D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAGO,SAAS,iBAAiB,KAAa;QAAE,WAAA,iEAAmB;IACjE,OAAO,AAAC,GAAsC,OAApC,aAAa,QAAQ,KAAK,WAAU;AAChD;AAGO,SAAS,iBAAiB,aAAqB;IACpD,MAAM,UAAqC;QACzC,wBAAwB;QACxB,sBAAsB;QACtB,oBAAoB;QACpB,iBAAiB;QACjB,kBAAkB;QAClB,uBAAuB;QACvB,eAAe;QACf,sBAAsB;QACtB,oBAAoB;IACtB;IAEA,OAAO,OAAO,CAAC,cAAc,IAAI;AACnC;AAGO,SAAS,kBAAkB,QAAgB;IAChD,MAAM,WAAsC;QAC1C,sBAAsB;QACtB,sBAAsB;QACtB,aAAa;QACb,iBAAiB;QACjB,mBAAmB;IACrB;IAEA,OAAO,QAAQ,CAAC,SAAS,IAAI;AAC/B;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO;yCAAI;YAAA;;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS;QAA6B,SAAA,iEAAiB;IAC5D,MAAM,OAAiB,EAAE;IACzB,IAAI,QAAQ,SAAS,gBAAgB;IAErC,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,kCAAkC;QAClC,MAAM,QAAQ,IAAI,OAAO,gBAAgB;QACzC,MAAM,cAAc,KAAK,GAAG,CAAC,AAAC,IAAI,KAAM,IAAI,KAAK,EAAE,IAAI,QAAQ,eAAe;QAC9E,MAAM,QAAQ,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,QAAQ,mBAAmB;QAEjE,QAAQ,UAAU,QAAQ,cAAc;QACxC,KAAK,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG,SAAS,sBAAsB;IACvD;IAEA,OAAO;AACT;AAGO,SAAS;IACd,OAAO;QACL,SAAS;QACT,oBAAoB;QACpB,oBAAoB;QACpB,cAAc;QACd,mBAAmB;QACnB,QAAQ;QACR,MAAM;QACN,MAAM;QACN,WAAW;QACX,MAAM;QACN,YAAY;IACd;AACF;AAGO,SAAS,uBAAuB,IAAW;IAChD,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;QACxB,OAAO;YAAE,OAAO;YAAO,SAAS;QAAwB;IAC1D;IAEA,IAAI,KAAK,MAAM,GAAG,GAAG;QACnB,OAAO;YAAE,OAAO;YAAO,SAAS;QAA2C;IAC7E;IAEA,MAAM,cAAc,KAAK,MAAM,CAAC,CAAA,IAAK,OAAO,MAAM,YAAY,CAAC,MAAM;IACrE,IAAI,YAAY,MAAM,GAAG,KAAK,MAAM,GAAG,KAAK;QAC1C,OAAO;YAAE,OAAO;YAAO,SAAS;QAA8C;IAChF;IAEA,OAAO;QAAE,OAAO;QAAM,SAAS;IAAgB;AACjD;AAGO,SAAS,wBAAwB,cAAmB;QAWrD;IAVJ,MAAM,WAAqB,EAAE;IAE7B,IAAI,2BAAA,qCAAA,eAAgB,iBAAiB,EAAE;QACrC,eAAe,iBAAiB,CAAC,OAAO,CAAC,CAAC;YACxC,IAAI,QAAQ,OAAO,EAAE;gBACnB,SAAS,IAAI,CAAC,QAAQ,OAAO;YAC/B;QACF;IACF;IAEA,IAAI,2BAAA,sCAAA,0BAAA,eAAgB,OAAO,cAAvB,8CAAA,wBAAyB,WAAW,EAAE;QACxC,SAAS,IAAI,CAAC,eAAe,OAAO,CAAC,WAAW;IAClD;IAEA,IAAI,2BAAA,qCAAA,eAAgB,eAAe,EAAE;QACnC,eAAe,eAAe,CAAC,OAAO,CAAC,CAAC;YACtC,IAAI,OAAO,QAAQ,UAAU;gBAC3B,SAAS,IAAI,CAAC;YAChB,OAAO,IAAI,IAAI,cAAc,EAAE;gBAC7B,SAAS,IAAI,CAAC,IAAI,cAAc;YAClC;QACF;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/src/components/algorithm-library.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport type { AlgorithmsResponse } from '@/lib/api';\nimport { getAlgorithmIcon, getAlgorithmColor, debounce } from '@/lib/utils';\n\ninterface AlgorithmLibraryProps {\n  algorithms: AlgorithmsResponse;\n  onAlgorithmSelect: (algorithm: any, category: string) => void;\n}\n\nexport function AlgorithmLibrary({ algorithms, onAlgorithmSelect }: AlgorithmLibraryProps) {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState<string>('all');\n  const [sortBy, setSortBy] = useState<'name' | 'category'>('category');\n\n  // Debounced search\n  const debouncedSearch = debounce((term: string) => {\n    setSearchTerm(term);\n  }, 300);\n\n  // Flatten algorithms for filtering and sorting\n  const allAlgorithms = [];\n  for (const [categoryName, categoryAlgorithms] of Object.entries(algorithms.algorithms)) {\n    for (const [algorithmKey, algorithm] of Object.entries(categoryAlgorithms)) {\n      allAlgorithms.push({\n        key: algorithm<PERSON>ey,\n        category: categoryName,\n        ...algorithm,\n      });\n    }\n  }\n\n  // Filter algorithms\n  const filteredAlgorithms = allAlgorithms.filter((algorithm) => {\n    const matchesSearch = searchTerm === '' || \n      algorithm.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      algorithm.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      algorithm.capabilities.some((cap: string) => \n        cap.toLowerCase().includes(searchTerm.toLowerCase())\n      );\n    \n    const matchesCategory = selectedCategory === 'all' || algorithm.category === selectedCategory;\n    \n    return matchesSearch && matchesCategory;\n  });\n\n  // Sort algorithms\n  const sortedAlgorithms = [...filteredAlgorithms].sort((a, b) => {\n    if (sortBy === 'name') {\n      return a.name.localeCompare(b.name);\n    } else {\n      return a.category.localeCompare(b.category);\n    }\n  });\n\n  const categories = ['all', ...algorithms.categories];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">Algorithm Library</h2>\n        <p className=\"text-gray-600\">\n          Discover and explore {algorithms.total_count} advanced algorithms for financial analysis and forecasting.\n        </p>\n      </div>\n\n      {/* Filters and Search */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <div className=\"flex flex-col lg:flex-row gap-4\">\n          {/* Search */}\n          <div className=\"flex-1\">\n            <label htmlFor=\"search\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Search Algorithms\n            </label>\n            <input\n              type=\"text\"\n              id=\"search\"\n              placeholder=\"Search by name, description, or capabilities...\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500\"\n              onChange={(e) => debouncedSearch(e.target.value)}\n            />\n          </div>\n\n          {/* Category Filter */}\n          <div>\n            <label htmlFor=\"category\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Category\n            </label>\n            <select\n              id=\"category\"\n              value={selectedCategory}\n              onChange={(e) => setSelectedCategory(e.target.value)}\n              className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500\"\n            >\n              {categories.map((category) => (\n                <option key={category} value={category}>\n                  {category === 'all' ? 'All Categories' : category.replace('_', ' ').replace(/\\b\\w/g, l => l.toUpperCase())}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          {/* Sort */}\n          <div>\n            <label htmlFor=\"sort\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Sort By\n            </label>\n            <select\n              id=\"sort\"\n              value={sortBy}\n              onChange={(e) => setSortBy(e.target.value as 'name' | 'category')}\n              className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500\"\n            >\n              <option value=\"category\">Category</option>\n              <option value=\"name\">Name</option>\n            </select>\n          </div>\n        </div>\n\n        {/* Results Count */}\n        <div className=\"mt-4 text-sm text-gray-600\">\n          Showing {sortedAlgorithms.length} of {algorithms.total_count} algorithms\n        </div>\n      </div>\n\n      {/* Algorithm Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {sortedAlgorithms.map((algorithm) => (\n          <div\n            key={`${algorithm.category}-${algorithm.key}`}\n            className=\"bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow cursor-pointer\"\n            onClick={() => onAlgorithmSelect(algorithm, algorithm.category)}\n          >\n            <div className=\"p-6\">\n              {/* Header */}\n              <div className=\"flex items-start justify-between mb-4\">\n                <div className=\"text-3xl\">{getAlgorithmIcon(algorithm.key)}</div>\n                <span className={`px-3 py-1 text-xs font-medium rounded-full ${getAlgorithmColor(algorithm.category)}`}>\n                  {algorithm.category.replace('_', ' ')}\n                </span>\n              </div>\n\n              {/* Content */}\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">{algorithm.name}</h3>\n              <p className=\"text-gray-600 text-sm mb-4 line-clamp-2\">{algorithm.description}</p>\n\n              {/* Capabilities */}\n              <div className=\"mb-4\">\n                <h4 className=\"text-xs font-medium text-gray-700 mb-2\">CAPABILITIES</h4>\n                <div className=\"flex flex-wrap gap-1\">\n                  {algorithm.capabilities.slice(0, 3).map((capability: string) => (\n                    <span\n                      key={capability}\n                      className=\"px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded\"\n                    >\n                      {capability.replace('_', ' ')}\n                    </span>\n                  ))}\n                  {algorithm.capabilities.length > 3 && (\n                    <span className=\"px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded\">\n                      +{algorithm.capabilities.length - 3} more\n                    </span>\n                  )}\n                </div>\n              </div>\n\n              {/* Input Requirements */}\n              <div className=\"mb-4\">\n                <h4 className=\"text-xs font-medium text-gray-700 mb-2\">REQUIRES</h4>\n                <div className=\"flex flex-wrap gap-1\">\n                  {algorithm.input_requirements.map((requirement: string) => (\n                    <span\n                      key={requirement}\n                      className=\"px-2 py-1 text-xs bg-blue-50 text-blue-700 rounded border border-blue-200\"\n                    >\n                      {requirement.replace('_', ' ')}\n                    </span>\n                  ))}\n                </div>\n              </div>\n\n              {/* Output Format */}\n              <div className=\"mb-4\">\n                <h4 className=\"text-xs font-medium text-gray-700 mb-1\">OUTPUT</h4>\n                <span className=\"text-xs text-gray-600\">\n                  {algorithm.output_format.replace('_', ' ')}\n                </span>\n              </div>\n\n              {/* Action Button */}\n              <button\n                onClick={(e) => {\n                  e.stopPropagation();\n                  onAlgorithmSelect(algorithm, algorithm.category);\n                }}\n                className=\"w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 transition-colors text-sm font-medium\"\n              >\n                Configure & Run\n              </button>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Empty State */}\n      {sortedAlgorithms.length === 0 && (\n        <div className=\"bg-white rounded-lg shadow-sm p-12 text-center\">\n          <div className=\"text-gray-400 text-6xl mb-4\">🔍</div>\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No algorithms found</h3>\n          <p className=\"text-gray-600 mb-4\">\n            Try adjusting your search terms or category filter.\n          </p>\n          <button\n            onClick={() => {\n              setSearchTerm('');\n              setSelectedCategory('all');\n            }}\n            className=\"bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors\"\n          >\n            Clear Filters\n          </button>\n        </div>\n      )}\n\n      {/* Category Overview */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Categories Overview</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          {algorithms.categories.map((category) => {\n            const categoryAlgorithms = Object.keys(algorithms.algorithms[category as keyof typeof algorithms.algorithms]).length;\n            return (\n              <div\n                key={category}\n                className={`p-4 rounded-lg border-2 cursor-pointer transition-colors ${\n                  selectedCategory === category\n                    ? 'border-indigo-500 bg-indigo-50'\n                    : 'border-gray-200 hover:border-gray-300'\n                }`}\n                onClick={() => setSelectedCategory(category)}\n              >\n                <div className={`text-sm font-medium mb-1 ${getAlgorithmColor(category).split(' ')[1]}`}>\n                  {category.replace('_', ' ').replace(/\\b\\w/g, l => l.toUpperCase())}\n                </div>\n                <div className=\"text-2xl font-bold text-gray-900\">{categoryAlgorithms}</div>\n                <div className=\"text-xs text-gray-600\">algorithms</div>\n              </div>\n            );\n          })}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;;;AAJA;;;AAWO,SAAS,iBAAiB,KAAwD;QAAxD,EAAE,UAAU,EAAE,iBAAiB,EAAyB,GAAxD;;IAC/B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IAE1D,mBAAmB;IACnB,MAAM,kBAAkB,CAAA,GAAA,sHAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QAChC,cAAc;IAChB,GAAG;IAEH,+CAA+C;IAC/C,MAAM,gBAAgB,EAAE;IACxB,KAAK,MAAM,CAAC,cAAc,mBAAmB,IAAI,OAAO,OAAO,CAAC,WAAW,UAAU,EAAG;QACtF,KAAK,MAAM,CAAC,cAAc,UAAU,IAAI,OAAO,OAAO,CAAC,oBAAqB;YAC1E,cAAc,IAAI,CAAC;gBACjB,KAAK;gBACL,UAAU;gBACV,GAAG,SAAS;YACd;QACF;IACF;IAEA,oBAAoB;IACpB,MAAM,qBAAqB,cAAc,MAAM,CAAC,CAAC;QAC/C,MAAM,gBAAgB,eAAe,MACnC,UAAU,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC5D,UAAU,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACnE,UAAU,YAAY,CAAC,IAAI,CAAC,CAAC,MAC3B,IAAI,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAGrD,MAAM,kBAAkB,qBAAqB,SAAS,UAAU,QAAQ,KAAK;QAE7E,OAAO,iBAAiB;IAC1B;IAEA,kBAAkB;IAClB,MAAM,mBAAmB;WAAI;KAAmB,CAAC,IAAI,CAAC,CAAC,GAAG;QACxD,IAAI,WAAW,QAAQ;YACrB,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;QACpC,OAAO;YACL,OAAO,EAAE,QAAQ,CAAC,aAAa,CAAC,EAAE,QAAQ;QAC5C;IACF;IAEA,MAAM,aAAa;QAAC;WAAU,WAAW,UAAU;KAAC;IAEpD,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAE,WAAU;;4BAAgB;4BACL,WAAW,WAAW;4BAAC;;;;;;;;;;;;;0BAKjD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,SAAQ;wCAAS,WAAU;kDAA+C;;;;;;kDAGjF,6LAAC;wCACC,MAAK;wCACL,IAAG;wCACH,aAAY;wCACZ,WAAU;wCACV,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;0CAKnD,6LAAC;;kDACC,6LAAC;wCAAM,SAAQ;wCAAW,WAAU;kDAA+C;;;;;;kDAGnF,6LAAC;wCACC,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;wCACnD,WAAU;kDAET,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;gDAAsB,OAAO;0DAC3B,aAAa,QAAQ,mBAAmB,SAAS,OAAO,CAAC,KAAK,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;+CAD5F;;;;;;;;;;;;;;;;0CAQnB,6LAAC;;kDACC,6LAAC;wCAAM,SAAQ;wCAAO,WAAU;kDAA+C;;;;;;kDAG/E,6LAAC;wCACC,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;wCACzC,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAW;;;;;;0DACzB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;;;;;;;;;;;;;;;;;;;kCAM3B,6LAAC;wBAAI,WAAU;;4BAA6B;4BACjC,iBAAiB,MAAM;4BAAC;4BAAK,WAAW,WAAW;4BAAC;;;;;;;;;;;;;0BAKjE,6LAAC;gBAAI,WAAU;0BACZ,iBAAiB,GAAG,CAAC,CAAC,0BACrB,6LAAC;wBAEC,WAAU;wBACV,SAAS,IAAM,kBAAkB,WAAW,UAAU,QAAQ;kCAE9D,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAY,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU,GAAG;;;;;;sDACzD,6LAAC;4CAAK,WAAW,AAAC,8CAAmF,OAAtC,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU,QAAQ;sDAChG,UAAU,QAAQ,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;8CAKrC,6LAAC;oCAAG,WAAU;8CAA4C,UAAU,IAAI;;;;;;8CACxE,6LAAC;oCAAE,WAAU;8CAA2C,UAAU,WAAW;;;;;;8CAG7E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,6LAAC;4CAAI,WAAU;;gDACZ,UAAU,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,2BACvC,6LAAC;wDAEC,WAAU;kEAET,WAAW,OAAO,CAAC,KAAK;uDAHpB;;;;;gDAMR,UAAU,YAAY,CAAC,MAAM,GAAG,mBAC/B,6LAAC;oDAAK,WAAU;;wDAAsD;wDAClE,UAAU,YAAY,CAAC,MAAM,GAAG;wDAAE;;;;;;;;;;;;;;;;;;;8CAO5C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,6LAAC;4CAAI,WAAU;sDACZ,UAAU,kBAAkB,CAAC,GAAG,CAAC,CAAC,4BACjC,6LAAC;oDAEC,WAAU;8DAET,YAAY,OAAO,CAAC,KAAK;mDAHrB;;;;;;;;;;;;;;;;8CAUb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,6LAAC;4CAAK,WAAU;sDACb,UAAU,aAAa,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;8CAK1C,6LAAC;oCACC,SAAS,CAAC;wCACR,EAAE,eAAe;wCACjB,kBAAkB,WAAW,UAAU,QAAQ;oCACjD;oCACA,WAAU;8CACX;;;;;;;;;;;;uBAnEE,AAAC,GAAwB,OAAtB,UAAU,QAAQ,EAAC,KAAiB,OAAd,UAAU,GAAG;;;;;;;;;;YA4EhD,iBAAiB,MAAM,KAAK,mBAC3B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAA8B;;;;;;kCAC7C,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,6LAAC;wBACC,SAAS;4BACP,cAAc;4BACd,oBAAoB;wBACtB;wBACA,WAAU;kCACX;;;;;;;;;;;;0BAOL,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;kCACZ,WAAW,UAAU,CAAC,GAAG,CAAC,CAAC;4BAC1B,MAAM,qBAAqB,OAAO,IAAI,CAAC,WAAW,UAAU,CAAC,SAA+C,EAAE,MAAM;4BACpH,qBACE,6LAAC;gCAEC,WAAW,AAAC,4DAIX,OAHC,qBAAqB,WACjB,mCACA;gCAEN,SAAS,IAAM,oBAAoB;;kDAEnC,6LAAC;wCAAI,WAAW,AAAC,4BAAqE,OAA1C,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU,KAAK,CAAC,IAAI,CAAC,EAAE;kDAClF,SAAS,OAAO,CAAC,KAAK,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;;;;;;kDAEjE,6LAAC;wCAAI,WAAU;kDAAoC;;;;;;kDACnD,6LAAC;wCAAI,WAAU;kDAAwB;;;;;;;+BAZlC;;;;;wBAeX;;;;;;;;;;;;;;;;;;AAKV;GAnPgB;KAAA", "debugId": null}}, {"offset": {"line": 700, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/src/lib/api.ts"], "sourcesContent": ["/**\n * API client for OneOptimizer backend\n */\n\nimport axios from 'axios';\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';\n\nexport const api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Types for API responses\nexport interface Algorithm {\n  name: string;\n  description: string;\n  capabilities: string[];\n  input_requirements: string[];\n  output_format: string;\n}\n\nexport interface AlgorithmCategory {\n  [key: string]: Algorithm;\n}\n\nexport interface AlgorithmsResponse {\n  success: boolean;\n  algorithms: {\n    econometric_models: AlgorithmCategory;\n    financial_analysis: AlgorithmCategory;\n    ai_agents: AlgorithmCategory;\n  };\n  total_count: number;\n  categories: string[];\n}\n\nexport interface AnalysisRequest {\n  data: any;\n  context?: string;\n  periods?: number;\n  method?: 'econometric' | 'ai' | 'hybrid';\n}\n\nexport interface AnalysisResponse {\n  success: boolean;\n  analysis?: any;\n  forecast?: any;\n  data?: any;\n  algorithm: string;\n  timestamp: string;\n  message?: string;\n}\n\n// API functions\nexport const apiClient = {\n  // Get available algorithms\n  async getAlgorithms(): Promise<AlgorithmsResponse> {\n    const response = await api.get('/api/algorithms');\n    return response.data;\n  },\n\n  // Health check\n  async healthCheck() {\n    const response = await api.get('/api/health');\n    return response.data;\n  },\n\n  // Time series analysis\n  async analyzeTimeSeries(request: AnalysisRequest): Promise<AnalysisResponse> {\n    const response = await api.post('/api/analysis/time-series', request);\n    return response.data;\n  },\n\n  // Forecasting\n  async createForecast(request: AnalysisRequest): Promise<AnalysisResponse> {\n    const response = await api.post('/api/analysis/forecast', request);\n    return response.data;\n  },\n\n  // Financial ratios analysis\n  async analyzeFinancialRatios(request: {\n    financial_data: any;\n    industry_type?: string;\n  }): Promise<AnalysisResponse> {\n    const response = await api.post('/api/analysis/financial-ratios', request);\n    return response.data;\n  },\n\n  // AI Agents\n  async runRevenueAgent(request: any): Promise<AnalysisResponse> {\n    const response = await api.post('/api/agents/revenue', request);\n    return response.data;\n  },\n\n  async runScenarioAgent(request: any): Promise<AnalysisResponse> {\n    const response = await api.post('/api/agents/scenario', request);\n    return response.data;\n  },\n\n  // Wizard processes\n  async runWizardProcess(request: {\n    process_type: string;\n    [key: string]: any;\n  }): Promise<AnalysisResponse> {\n    const response = await api.post('/api/wizard/process', request);\n    return response.data;\n  },\n\n  // Data management\n  async uploadDataFile(file: File, dataType: string, description?: string): Promise<any> {\n    const formData = new FormData();\n    formData.append('file', file);\n    formData.append('data_type', dataType);\n    if (description) {\n      formData.append('description', description);\n    }\n\n    const response = await api.post('/api/data/upload', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n    });\n    return response.data;\n  },\n\n  async listUploadedData(): Promise<any> {\n    const response = await api.get('/api/data/list');\n    return response.data;\n  },\n\n  // Macro economics\n  async getMacroIndicators(params: {\n    countries?: string;\n    indicators?: string;\n    years?: string;\n  }): Promise<any> {\n    const response = await api.get('/api/macro/indicators', { params });\n    return response.data;\n  },\n\n  async getAvailableCountries(): Promise<any> {\n    const response = await api.get('/api/macro/countries');\n    return response.data;\n  },\n\n  async getAvailableIndicators(): Promise<any> {\n    const response = await api.get('/api/macro/indicators/list');\n    return response.data;\n  },\n\n  async analyzeMacroData(request: {\n    countries: string[];\n    business_context?: string;\n    analysis_type?: string;\n  }): Promise<any> {\n    const response = await api.post('/api/macro/analysis', request);\n    return response.data;\n  },\n};\n\n// Error handling\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    console.error('API Error:', error.response?.data || error.message);\n    throw error;\n  }\n);\n\nexport default apiClient;\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;AAIoB;AAFrB;;AAEA,MAAM,eAAe,6DAAmC;AAEjD,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC9B,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AA4CO,MAAM,YAAY;IACvB,2BAA2B;IAC3B,MAAM;QACJ,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe;IACf,MAAM;QACJ,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,uBAAuB;IACvB,MAAM,mBAAkB,OAAwB;QAC9C,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,6BAA6B;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,MAAM,gBAAe,OAAwB;QAC3C,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,0BAA0B;QAC1D,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,MAAM,wBAAuB,OAG5B;QACC,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,kCAAkC;QAClE,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY;IACZ,MAAM,iBAAgB,OAAY;QAChC,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,uBAAuB;QACvD,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,kBAAiB,OAAY;QACjC,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,wBAAwB;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,mBAAmB;IACnB,MAAM,kBAAiB,OAGtB;QACC,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,uBAAuB;QACvD,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB;IAClB,MAAM,gBAAe,IAAU,EAAE,QAAgB,EAAE,WAAoB;QACrE,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QACxB,SAAS,MAAM,CAAC,aAAa;QAC7B,IAAI,aAAa;YACf,SAAS,MAAM,CAAC,eAAe;QACjC;QAEA,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,oBAAoB,UAAU;YAC5D,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM;QACJ,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB;IAClB,MAAM,oBAAmB,MAIxB;QACC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,yBAAyB;YAAE;QAAO;QACjE,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM;QACJ,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM;QACJ,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,kBAAiB,OAItB;QACC,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,uBAAuB;QACvD,OAAO,SAAS,IAAI;IACtB;AACF;AAEA,iBAAiB;AACjB,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC3B,CAAC,WAAa,UACd,CAAC;QAC6B;IAA5B,QAAQ,KAAK,CAAC,cAAc,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,IAAI,KAAI,MAAM,OAAO;IACjE,MAAM;AACR;uCAGa", "debugId": null}}, {"offset": {"line": 812, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/src/components/analysis-workspace.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { apiClient } from '@/lib/api';\nimport { getAlgorithmIcon, getAlgorithmColor, generateSampleTimeSeriesData, generateSampleFinancialData, validateTimeSeriesData, extractBusinessInsights } from '@/lib/utils';\n\ninterface AnalysisWorkspaceProps {\n  algorithm: any;\n  onBack: () => void;\n}\n\nexport function AnalysisWorkspace({ algorithm, onBack }: AnalysisWorkspaceProps) {\n  const [isRunning, setIsRunning] = useState(false);\n  const [results, setResults] = useState<any>(null);\n  const [error, setError] = useState<string | null>(null);\n  const [parameters, setParameters] = useState<any>({});\n  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);\n  const [selectedDataFile, setSelectedDataFile] = useState<string>('');\n  const [useRealData, setUseRealData] = useState(false);\n\n  // Debug logging\n  console.log('AnalysisWorkspace received algorithm:', algorithm);\n\n  // Load uploaded files on component mount\n  useEffect(() => {\n    loadUploadedFiles();\n  }, []);\n\n  const loadUploadedFiles = async () => {\n    try {\n      const response = await apiClient.listUploadedData();\n      if (response.success) {\n        setUploadedFiles(response.files);\n      }\n    } catch (err) {\n      console.error('Error loading uploaded files:', err);\n    }\n  };\n\n  const handleRunAnalysis = async () => {\n    try {\n      setIsRunning(true);\n      setError(null);\n      \n      let response;\n      \n      // Prepare data based on source selection\n      let analysisData;\n\n      if (useRealData && selectedDataFile) {\n        // Use uploaded data file\n        analysisData = {\n          data_source: 'uploaded_file',\n          file_path: selectedDataFile,\n          use_real_data: true\n        };\n      } else {\n        // Use sample data\n        if (algorithm.key === 'time_series_analyzer' || algorithm.key === 'forecasting_engine') {\n          const sampleData = generateSampleTimeSeriesData(24);\n          const validation = validateTimeSeriesData(sampleData);\n\n          if (!validation.valid) {\n            throw new Error(validation.message);\n          }\n\n          analysisData = {\n            data: sampleData,\n            data_source: 'sample_data',\n            use_real_data: false\n          };\n        } else if (algorithm.key === 'financial_ratios') {\n          analysisData = {\n            data: generateSampleFinancialData(),\n            data_source: 'sample_data',\n            use_real_data: false\n          };\n        }\n      }\n\n      // Execute analysis based on algorithm type\n      if (algorithm.key === 'time_series_analyzer') {\n        response = await apiClient.analyzeTimeSeries({\n          ...analysisData,\n          context: parameters.context || 'revenue',\n        });\n      } else if (algorithm.key === 'forecasting_engine') {\n        response = await apiClient.createForecast({\n          ...analysisData,\n          periods: parameters.periods || 12,\n          method: parameters.method || 'econometric',\n          context: parameters.context || 'revenue',\n        });\n      } else if (algorithm.key === 'financial_ratios') {\n        response = await apiClient.analyzeFinancialRatios({\n          ...(useRealData ? analysisData : { financial_data: analysisData.data }),\n          industry_type: parameters.industry_type || 'telecom',\n        });\n      } else if (algorithm.key === 'revenue_agent') {\n        response = await apiClient.runRevenueAgent({\n          business_units: parameters.business_units || ['Virgin Mobile Chile', 'Virgin Mobile Colombia'],\n          forecast_periods: parameters.periods || 12,\n          scenarios: parameters.scenarios || ['Base', 'Optimistic', 'Pessimistic'],\n        });\n      } else if (algorithm.key === 'scenario_agent') {\n        response = await apiClient.runScenarioAgent({\n          base_assumptions: parameters.base_assumptions || {\n            revenue_growth: 0.15,\n            cost_inflation: 0.05,\n            market_expansion: 0.10\n          },\n          risk_parameters: parameters.risk_parameters || {\n            economic_volatility: 0.2,\n            competitive_pressure: 0.15,\n            regulatory_risk: 0.1\n          },\n          simulation_count: parameters.simulation_count || 1000,\n        });\n      } else {\n        // Generic algorithm execution\n        response = await apiClient.runWizardProcess({\n          process_type: 'comprehensive',\n          algorithm: algorithm.key,\n          parameters: parameters,\n        });\n      }\n      \n      setResults(response);\n    } catch (err: any) {\n      setError(err.response?.data?.detail || err.message || 'Analysis failed');\n      console.error('Analysis error:', err);\n    } finally {\n      setIsRunning(false);\n    }\n  };\n\n  const handleParameterChange = (key: string, value: any) => {\n    setParameters((prev: any) => ({ ...prev, [key]: value }));\n  };\n\n  const renderParameterControls = () => {\n    const controls = [];\n\n    // Data source selection\n    controls.push(\n      <div key=\"data_source\" className=\"mb-4\">\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Data Source\n        </label>\n        <div className=\"space-y-2\">\n          <label className=\"flex items-center\">\n            <input\n              type=\"radio\"\n              name=\"data_source\"\n              checked={!useRealData}\n              onChange={() => setUseRealData(false)}\n              className=\"mr-2\"\n            />\n            <span>Use Sample Data (Demo)</span>\n          </label>\n          <label className=\"flex items-center\">\n            <input\n              type=\"radio\"\n              name=\"data_source\"\n              checked={useRealData}\n              onChange={() => setUseRealData(true)}\n              className=\"mr-2\"\n              disabled={uploadedFiles.length === 0}\n            />\n            <span>Use Uploaded Data {uploadedFiles.length === 0 && '(No files available)'}</span>\n          </label>\n        </div>\n\n        {useRealData && uploadedFiles.length > 0 && (\n          <select\n            value={selectedDataFile}\n            onChange={(e) => setSelectedDataFile(e.target.value)}\n            className=\"mt-2 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n          >\n            <option value=\"\">Select a data file...</option>\n            {uploadedFiles.map((file, index) => (\n              <option key={index} value={file.file_path}>\n                {file.filename} ({file.data_type.toUpperCase()})\n              </option>\n            ))}\n          </select>\n        )}\n      </div>\n    );\n\n    // Common parameters based on algorithm type\n    if (algorithm.input_requirements.includes('time_series_data')) {\n      controls.push(\n        <div key=\"context\" className=\"mb-4\">\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Business Context\n          </label>\n          <select\n            value={parameters.context || 'revenue'}\n            onChange={(e) => handleParameterChange('context', e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n          >\n            <option value=\"revenue\">Revenue</option>\n            <option value=\"costs\">Costs</option>\n            <option value=\"customers\">Customers</option>\n            <option value=\"arpu\">ARPU</option>\n          </select>\n        </div>\n      );\n    }\n    \n    if (algorithm.input_requirements.includes('forecast_periods')) {\n      controls.push(\n        <div key=\"periods\" className=\"mb-4\">\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Forecast Periods\n          </label>\n          <input\n            type=\"number\"\n            min=\"1\"\n            max=\"60\"\n            value={parameters.periods || 12}\n            onChange={(e) => handleParameterChange('periods', parseInt(e.target.value))}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n          />\n        </div>\n      );\n    }\n    \n    if (algorithm.key === 'forecasting_engine') {\n      controls.push(\n        <div key=\"method\" className=\"mb-4\">\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Forecasting Method\n          </label>\n          <select\n            value={parameters.method || 'econometric'}\n            onChange={(e) => handleParameterChange('method', e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n          >\n            <option value=\"econometric\">Econometric</option>\n            <option value=\"ai\">AI-Powered</option>\n            <option value=\"hybrid\">Hybrid</option>\n          </select>\n        </div>\n      );\n    }\n    \n    if (algorithm.key === 'financial_ratios') {\n      controls.push(\n        <div key=\"industry\" className=\"mb-4\">\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Industry Type\n          </label>\n          <select\n            value={parameters.industry_type || 'telecom'}\n            onChange={(e) => handleParameterChange('industry_type', e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n          >\n            <option value=\"telecom\">Telecommunications</option>\n            <option value=\"fintech\">Fintech</option>\n            <option value=\"general\">General</option>\n          </select>\n        </div>\n      );\n    }\n    \n    return controls;\n  };\n\n  const renderResults = () => {\n    if (!results) return null;\n    \n    const insights = extractBusinessInsights(results.analysis || results.data || results);\n    \n    return (\n      <div className=\"space-y-6\">\n        {/* Success Message */}\n        <div className=\"bg-green-50 border border-green-200 rounded-lg p-4\">\n          <div className=\"flex items-center\">\n            <div className=\"text-green-600 text-xl mr-3\">✅</div>\n            <div>\n              <h4 className=\"font-medium text-green-800\">Analysis Complete</h4>\n              <p className=\"text-green-700 text-sm\">\n                {algorithm.name} executed successfully at {new Date(results.timestamp).toLocaleTimeString()}\n              </p>\n            </div>\n          </div>\n        </div>\n        \n        {/* Business Insights */}\n        {insights.length > 0 && (\n          <div className=\"bg-white rounded-lg border p-6\">\n            <h4 className=\"font-semibold text-gray-900 mb-4\">💡 Key Insights</h4>\n            <div className=\"space-y-3\">\n              {insights.map((insight, index) => (\n                <div key={index} className=\"flex items-start\">\n                  <div className=\"text-blue-600 mr-3 mt-1\">•</div>\n                  <p className=\"text-gray-700\">{insight}</p>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n        \n        {/* Raw Results */}\n        <div className=\"bg-white rounded-lg border p-6\">\n          <h4 className=\"font-semibold text-gray-900 mb-4\">📊 Detailed Results</h4>\n          <div className=\"bg-gray-50 rounded-lg p-4 overflow-auto max-h-96\">\n            <pre className=\"text-sm text-gray-700\">\n              {JSON.stringify(results, null, 2)}\n            </pre>\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center\">\n            <button\n              onClick={onBack}\n              className=\"mr-4 p-2 text-gray-400 hover:text-gray-600 transition-colors\"\n            >\n              ← Back\n            </button>\n            <div className=\"text-3xl mr-4\">{getAlgorithmIcon(algorithm.key)}</div>\n            <div>\n              <h2 className=\"text-2xl font-bold text-gray-900\">{algorithm.name}</h2>\n              <p className=\"text-gray-600\">{algorithm.description}</p>\n            </div>\n          </div>\n          <span className={`px-3 py-1 text-sm font-medium rounded-full ${getAlgorithmColor(algorithm.category)}`}>\n            {algorithm.category.replace('_', ' ')}\n          </span>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* Configuration Panel */}\n        <div className=\"lg:col-span-1\">\n          <div className=\"bg-white rounded-lg shadow-sm p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">⚙️ Configuration</h3>\n            \n            {/* Algorithm Info */}\n            <div className=\"mb-6\">\n              <h4 className=\"text-sm font-medium text-gray-700 mb-2\">CAPABILITIES</h4>\n              <div className=\"space-y-1\">\n                {algorithm.capabilities.map((capability: string) => (\n                  <div key={capability} className=\"text-sm text-gray-600\">\n                    • {capability.replace('_', ' ')}\n                  </div>\n                ))}\n              </div>\n            </div>\n            \n            {/* Parameter Controls */}\n            <div className=\"mb-6\">\n              <h4 className=\"text-sm font-medium text-gray-700 mb-3\">PARAMETERS</h4>\n              {renderParameterControls()}\n            </div>\n            \n            {/* Sample Data Info */}\n            <div className=\"mb-6 p-3 bg-blue-50 rounded-lg\">\n              <h4 className=\"text-sm font-medium text-blue-800 mb-1\">📊 Sample Data</h4>\n              <p className=\"text-xs text-blue-700\">\n                This demo uses generated sample data. In production, you would upload your own data files.\n              </p>\n            </div>\n            \n            {/* Run Button */}\n            <button\n              onClick={handleRunAnalysis}\n              disabled={isRunning}\n              className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${\n                isRunning\n                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                  : 'bg-indigo-600 text-white hover:bg-indigo-700'\n              }`}\n            >\n              {isRunning ? (\n                <div className=\"flex items-center justify-center\">\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                  Running Analysis...\n                </div>\n              ) : (\n                '🚀 Run Analysis'\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Results Panel */}\n        <div className=\"lg:col-span-2\">\n          <div className=\"bg-white rounded-lg shadow-sm p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">📈 Results</h3>\n            \n            {error && (\n              <div className=\"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"text-red-600 text-xl mr-3\">❌</div>\n                  <div>\n                    <h4 className=\"font-medium text-red-800\">Analysis Failed</h4>\n                    <p className=\"text-red-700 text-sm\">{error}</p>\n                  </div>\n                </div>\n              </div>\n            )}\n            \n            {!results && !error && !isRunning && (\n              <div className=\"text-center py-12\">\n                <div className=\"text-gray-400 text-6xl mb-4\">📊</div>\n                <h4 className=\"text-lg font-medium text-gray-900 mb-2\">Ready to Analyze</h4>\n                <p className=\"text-gray-600\">\n                  Configure your parameters and click \"Run Analysis\" to see results.\n                </p>\n              </div>\n            )}\n            \n            {isRunning && (\n              <div className=\"text-center py-12\">\n                <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4\"></div>\n                <h4 className=\"text-lg font-medium text-gray-900 mb-2\">Running Analysis</h4>\n                <p className=\"text-gray-600\">\n                  {algorithm.name} is processing your data...\n                </p>\n              </div>\n            )}\n            \n            {results && renderResults()}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAWO,SAAS,kBAAkB,KAA6C;QAA7C,EAAE,SAAS,EAAE,MAAM,EAA0B,GAA7C;;IAChC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAC5C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO,CAAC;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC5D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,gBAAgB;IAChB,QAAQ,GAAG,CAAC,yCAAyC;IAErD,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR;QACF;sCAAG,EAAE;IAEL,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,gBAAgB;YACjD,IAAI,SAAS,OAAO,EAAE;gBACpB,iBAAiB,SAAS,KAAK;YACjC;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,iCAAiC;QACjD;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI;YACF,aAAa;YACb,SAAS;YAET,IAAI;YAEJ,yCAAyC;YACzC,IAAI;YAEJ,IAAI,eAAe,kBAAkB;gBACnC,yBAAyB;gBACzB,eAAe;oBACb,aAAa;oBACb,WAAW;oBACX,eAAe;gBACjB;YACF,OAAO;gBACL,kBAAkB;gBAClB,IAAI,UAAU,GAAG,KAAK,0BAA0B,UAAU,GAAG,KAAK,sBAAsB;oBACtF,MAAM,aAAa,CAAA,GAAA,sHAAA,CAAA,+BAA4B,AAAD,EAAE;oBAChD,MAAM,aAAa,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD,EAAE;oBAE1C,IAAI,CAAC,WAAW,KAAK,EAAE;wBACrB,MAAM,IAAI,MAAM,WAAW,OAAO;oBACpC;oBAEA,eAAe;wBACb,MAAM;wBACN,aAAa;wBACb,eAAe;oBACjB;gBACF,OAAO,IAAI,UAAU,GAAG,KAAK,oBAAoB;oBAC/C,eAAe;wBACb,MAAM,CAAA,GAAA,sHAAA,CAAA,8BAA2B,AAAD;wBAChC,aAAa;wBACb,eAAe;oBACjB;gBACF;YACF;YAEA,2CAA2C;YAC3C,IAAI,UAAU,GAAG,KAAK,wBAAwB;gBAC5C,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,iBAAiB,CAAC;oBAC3C,GAAG,YAAY;oBACf,SAAS,WAAW,OAAO,IAAI;gBACjC;YACF,OAAO,IAAI,UAAU,GAAG,KAAK,sBAAsB;gBACjD,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,cAAc,CAAC;oBACxC,GAAG,YAAY;oBACf,SAAS,WAAW,OAAO,IAAI;oBAC/B,QAAQ,WAAW,MAAM,IAAI;oBAC7B,SAAS,WAAW,OAAO,IAAI;gBACjC;YACF,OAAO,IAAI,UAAU,GAAG,KAAK,oBAAoB;gBAC/C,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,sBAAsB,CAAC;oBAChD,GAAI,cAAc,eAAe;wBAAE,gBAAgB,aAAa,IAAI;oBAAC,CAAC;oBACtE,eAAe,WAAW,aAAa,IAAI;gBAC7C;YACF,OAAO,IAAI,UAAU,GAAG,KAAK,iBAAiB;gBAC5C,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,eAAe,CAAC;oBACzC,gBAAgB,WAAW,cAAc,IAAI;wBAAC;wBAAuB;qBAAyB;oBAC9F,kBAAkB,WAAW,OAAO,IAAI;oBACxC,WAAW,WAAW,SAAS,IAAI;wBAAC;wBAAQ;wBAAc;qBAAc;gBAC1E;YACF,OAAO,IAAI,UAAU,GAAG,KAAK,kBAAkB;gBAC7C,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC;oBAC1C,kBAAkB,WAAW,gBAAgB,IAAI;wBAC/C,gBAAgB;wBAChB,gBAAgB;wBAChB,kBAAkB;oBACpB;oBACA,iBAAiB,WAAW,eAAe,IAAI;wBAC7C,qBAAqB;wBACrB,sBAAsB;wBACtB,iBAAiB;oBACnB;oBACA,kBAAkB,WAAW,gBAAgB,IAAI;gBACnD;YACF,OAAO;gBACL,8BAA8B;gBAC9B,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC;oBAC1C,cAAc;oBACd,WAAW,UAAU,GAAG;oBACxB,YAAY;gBACd;YACF;YAEA,WAAW;QACb,EAAE,OAAO,KAAU;gBACR,oBAAA;YAAT,SAAS,EAAA,gBAAA,IAAI,QAAQ,cAAZ,qCAAA,qBAAA,cAAc,IAAI,cAAlB,yCAAA,mBAAoB,MAAM,KAAI,IAAI,OAAO,IAAI;YACtD,QAAQ,KAAK,CAAC,mBAAmB;QACnC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,wBAAwB,CAAC,KAAa;QAC1C,cAAc,CAAC,OAAc,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,IAAI,EAAE;YAAM,CAAC;IACzD;IAEA,MAAM,0BAA0B;QAC9B,MAAM,WAAW,EAAE;QAEnB,wBAAwB;QACxB,SAAS,IAAI,eACX,6LAAC;YAAsB,WAAU;;8BAC/B,6LAAC;oBAAM,WAAU;8BAA+C;;;;;;8BAGhE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAM,WAAU;;8CACf,6LAAC;oCACC,MAAK;oCACL,MAAK;oCACL,SAAS,CAAC;oCACV,UAAU,IAAM,eAAe;oCAC/B,WAAU;;;;;;8CAEZ,6LAAC;8CAAK;;;;;;;;;;;;sCAER,6LAAC;4BAAM,WAAU;;8CACf,6LAAC;oCACC,MAAK;oCACL,MAAK;oCACL,SAAS;oCACT,UAAU,IAAM,eAAe;oCAC/B,WAAU;oCACV,UAAU,cAAc,MAAM,KAAK;;;;;;8CAErC,6LAAC;;wCAAK;wCAAmB,cAAc,MAAM,KAAK,KAAK;;;;;;;;;;;;;;;;;;;gBAI1D,eAAe,cAAc,MAAM,GAAG,mBACrC,6LAAC;oBACC,OAAO;oBACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;oBACnD,WAAU;;sCAEV,6LAAC;4BAAO,OAAM;sCAAG;;;;;;wBAChB,cAAc,GAAG,CAAC,CAAC,MAAM,sBACxB,6LAAC;gCAAmB,OAAO,KAAK,SAAS;;oCACtC,KAAK,QAAQ;oCAAC;oCAAG,KAAK,SAAS,CAAC,WAAW;oCAAG;;+BADpC;;;;;;;;;;;;WApCZ;;;;;QA6CX,4CAA4C;QAC5C,IAAI,UAAU,kBAAkB,CAAC,QAAQ,CAAC,qBAAqB;YAC7D,SAAS,IAAI,eACX,6LAAC;gBAAkB,WAAU;;kCAC3B,6LAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,6LAAC;wBACC,OAAO,WAAW,OAAO,IAAI;wBAC7B,UAAU,CAAC,IAAM,sBAAsB,WAAW,EAAE,MAAM,CAAC,KAAK;wBAChE,WAAU;;0CAEV,6LAAC;gCAAO,OAAM;0CAAU;;;;;;0CACxB,6LAAC;gCAAO,OAAM;0CAAQ;;;;;;0CACtB,6LAAC;gCAAO,OAAM;0CAAY;;;;;;0CAC1B,6LAAC;gCAAO,OAAM;0CAAO;;;;;;;;;;;;;eAZhB;;;;;QAgBb;QAEA,IAAI,UAAU,kBAAkB,CAAC,QAAQ,CAAC,qBAAqB;YAC7D,SAAS,IAAI,eACX,6LAAC;gBAAkB,WAAU;;kCAC3B,6LAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,6LAAC;wBACC,MAAK;wBACL,KAAI;wBACJ,KAAI;wBACJ,OAAO,WAAW,OAAO,IAAI;wBAC7B,UAAU,CAAC,IAAM,sBAAsB,WAAW,SAAS,EAAE,MAAM,CAAC,KAAK;wBACzE,WAAU;;;;;;;eAVL;;;;;QAcb;QAEA,IAAI,UAAU,GAAG,KAAK,sBAAsB;YAC1C,SAAS,IAAI,eACX,6LAAC;gBAAiB,WAAU;;kCAC1B,6LAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,6LAAC;wBACC,OAAO,WAAW,MAAM,IAAI;wBAC5B,UAAU,CAAC,IAAM,sBAAsB,UAAU,EAAE,MAAM,CAAC,KAAK;wBAC/D,WAAU;;0CAEV,6LAAC;gCAAO,OAAM;0CAAc;;;;;;0CAC5B,6LAAC;gCAAO,OAAM;0CAAK;;;;;;0CACnB,6LAAC;gCAAO,OAAM;0CAAS;;;;;;;;;;;;;eAXlB;;;;;QAeb;QAEA,IAAI,UAAU,GAAG,KAAK,oBAAoB;YACxC,SAAS,IAAI,eACX,6LAAC;gBAAmB,WAAU;;kCAC5B,6LAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,6LAAC;wBACC,OAAO,WAAW,aAAa,IAAI;wBACnC,UAAU,CAAC,IAAM,sBAAsB,iBAAiB,EAAE,MAAM,CAAC,KAAK;wBACtE,WAAU;;0CAEV,6LAAC;gCAAO,OAAM;0CAAU;;;;;;0CACxB,6LAAC;gCAAO,OAAM;0CAAU;;;;;;0CACxB,6LAAC;gCAAO,OAAM;0CAAU;;;;;;;;;;;;;eAXnB;;;;;QAeb;QAEA,OAAO;IACT;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,SAAS,OAAO;QAErB,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,0BAAuB,AAAD,EAAE,QAAQ,QAAQ,IAAI,QAAQ,IAAI,IAAI;QAE7E,qBACE,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAA8B;;;;;;0CAC7C,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAE,WAAU;;4CACV,UAAU,IAAI;4CAAC;4CAA2B,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;gBAOhG,SAAS,MAAM,GAAG,mBACjB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;oCAAgB,WAAU;;sDACzB,6LAAC;4CAAI,WAAU;sDAA0B;;;;;;sDACzC,6LAAC;4CAAE,WAAU;sDAAiB;;;;;;;mCAFtB;;;;;;;;;;;;;;;;8BAUlB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ,KAAK,SAAS,CAAC,SAAS,MAAM;;;;;;;;;;;;;;;;;;;;;;;IAM3C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCAAI,WAAU;8CAAiB,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU,GAAG;;;;;;8CAC9D,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAoC,UAAU,IAAI;;;;;;sDAChE,6LAAC;4CAAE,WAAU;sDAAiB,UAAU,WAAW;;;;;;;;;;;;;;;;;;sCAGvD,6LAAC;4BAAK,WAAW,AAAC,8CAAmF,OAAtC,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU,QAAQ;sCAChG,UAAU,QAAQ,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;0BAKvC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAGzD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,6LAAC;4CAAI,WAAU;sDACZ,UAAU,YAAY,CAAC,GAAG,CAAC,CAAC,2BAC3B,6LAAC;oDAAqB,WAAU;;wDAAwB;wDACnD,WAAW,OAAO,CAAC,KAAK;;mDADnB;;;;;;;;;;;;;;;;8CAQhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAyC;;;;;;wCACtD;;;;;;;8CAIH,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAMvC,6LAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAW,AAAC,6DAIX,OAHC,YACI,iDACA;8CAGL,0BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;4CAAuE;;;;;;+CAIxF;;;;;;;;;;;;;;;;;kCAOR,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;gCAExD,uBACC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA4B;;;;;;0DAC3C,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA2B;;;;;;kEACzC,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;gCAM5C,CAAC,WAAW,CAAC,SAAS,CAAC,2BACtB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAA8B;;;;;;sDAC7C,6LAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;gCAMhC,2BACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,6LAAC;4CAAE,WAAU;;gDACV,UAAU,IAAI;gDAAC;;;;;;;;;;;;;gCAKrB,WAAW;;;;;;;;;;;;;;;;;;;;;;;;AAMxB;GA5agB;KAAA", "debugId": null}}, {"offset": {"line": 1802, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/src/components/dashboard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport type { AlgorithmsResponse } from '@/lib/api';\nimport { getAlgorithmIcon, getAlgorithmColor, generateSampleTimeSeriesData } from '@/lib/utils';\n\ninterface DashboardProps {\n  algorithms: AlgorithmsResponse | null;\n  onAlgorithmSelect: (algorithm: any, category: string) => void;\n  onViewChange: (view: 'dashboard' | 'algorithms' | 'workspace') => void;\n}\n\nexport function Dashboard({ algorithms, onAlgorithmSelect, onViewChange }: DashboardProps) {\n  const [selectedCategory, setSelectedCategory] = useState<string>('all');\n\n  if (!algorithms) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"text-gray-500\">Loading algorithms...</div>\n      </div>\n    );\n  }\n\n  const totalAlgorithms = algorithms.total_count;\n  const categories = algorithms.categories;\n\n  // Get featured algorithms (first 3 from each category)\n  const featuredAlgorithms: any[] = [];\n  for (const [categoryName, categoryAlgorithms] of Object.entries(algorithms.algorithms)) {\n    const algorithmEntries = Object.entries(categoryAlgorithms).slice(0, 2);\n    for (const [algorithmKey, algorithm] of algorithmEntries) {\n      featuredAlgorithms.push({\n        key: algorithmKey,\n        category: categoryName,\n        ...algorithm,\n      });\n    }\n  }\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Welcome Section */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <div className=\"text-center\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            Welcome to OneOptimizer v2.0\n          </h2>\n          <p className=\"text-lg text-gray-600 mb-6\">\n            AI-Powered Budgeting System with Advanced Analytics\n          </p>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto\">\n            <div className=\"text-center\">\n              <div className=\"text-4xl mb-2\">🧮</div>\n              <h3 className=\"font-semibold text-gray-900\">Advanced Algorithms</h3>\n              <p className=\"text-gray-600\">Bayesian forecasting, econometric models, and AI agents</p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-4xl mb-2\">📊</div>\n              <h3 className=\"font-semibold text-gray-900\">Interactive Analysis</h3>\n              <p className=\"text-gray-600\">Real-time parameter adjustment and visualization</p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-4xl mb-2\">🎯</div>\n              <h3 className=\"font-semibold text-gray-900\">Business Insights</h3>\n              <p className=\"text-gray-600\">Clear recommendations and actionable insights</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Quick Stats */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <div className=\"bg-white rounded-lg shadow-sm p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"text-2xl mr-3\">🧮</div>\n            <div>\n              <div className=\"text-2xl font-bold text-gray-900\">{totalAlgorithms}</div>\n              <div className=\"text-sm text-gray-600\">Available Algorithms</div>\n            </div>\n          </div>\n        </div>\n        <div className=\"bg-white rounded-lg shadow-sm p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"text-2xl mr-3\">📂</div>\n            <div>\n              <div className=\"text-2xl font-bold text-gray-900\">{categories.length}</div>\n              <div className=\"text-sm text-gray-600\">Algorithm Categories</div>\n            </div>\n          </div>\n        </div>\n        <div className=\"bg-white rounded-lg shadow-sm p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"text-2xl mr-3\">🤖</div>\n            <div>\n              <div className=\"text-2xl font-bold text-gray-900\">7</div>\n              <div className=\"text-sm text-gray-600\">AI Agents</div>\n            </div>\n          </div>\n        </div>\n        <div className=\"bg-white rounded-lg shadow-sm p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"text-2xl mr-3\">⚡</div>\n            <div>\n              <div className=\"text-2xl font-bold text-gray-900\">Ready</div>\n              <div className=\"text-sm text-gray-600\">System Status</div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">Quick Actions</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <button\n            onClick={() => onViewChange('algorithms')}\n            className=\"p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-indigo-400 hover:bg-indigo-50 transition-colors text-left\"\n          >\n            <div className=\"text-2xl mb-2\">🔍</div>\n            <h4 className=\"font-medium text-gray-900\">Explore Algorithms</h4>\n            <p className=\"text-sm text-gray-600\">Browse and discover available analysis algorithms</p>\n          </button>\n          <button\n            onClick={() => {\n              // Select a sample algorithm for demo\n              const sampleAlgorithm = featuredAlgorithms[0];\n              if (sampleAlgorithm) {\n                onAlgorithmSelect(sampleAlgorithm, sampleAlgorithm.category);\n              }\n            }}\n            className=\"p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-green-400 hover:bg-green-50 transition-colors text-left\"\n          >\n            <div className=\"text-2xl mb-2\">🚀</div>\n            <h4 className=\"font-medium text-gray-900\">Quick Analysis</h4>\n            <p className=\"text-sm text-gray-600\">Start analyzing with sample data</p>\n          </button>\n          <button\n            onClick={() => {\n              // Generate sample data and start analysis\n              const sampleData = generateSampleTimeSeriesData();\n              console.log('Generated sample data:', sampleData);\n            }}\n            className=\"p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-purple-400 hover:bg-purple-50 transition-colors text-left\"\n          >\n            <div className=\"text-2xl mb-2\">📊</div>\n            <h4 className=\"font-medium text-gray-900\">Sample Data</h4>\n            <p className=\"text-sm text-gray-600\">Generate sample data for testing</p>\n          </button>\n        </div>\n      </div>\n\n      {/* Featured Algorithms */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">Featured Algorithms</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n          {featuredAlgorithms.slice(0, 6).map((algorithm) => (\n            <div\n              key={`${algorithm.category}-${algorithm.key}`}\n              className=\"border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer\"\n              onClick={() => onAlgorithmSelect(algorithm, algorithm.category)}\n            >\n              <div className=\"flex items-start justify-between mb-3\">\n                <div className=\"text-2xl\">{getAlgorithmIcon(algorithm.key)}</div>\n                <span className={`px-2 py-1 text-xs rounded-full ${getAlgorithmColor(algorithm.category)}`}>\n                  {algorithm.category.replace('_', ' ')}\n                </span>\n              </div>\n              <h4 className=\"font-medium text-gray-900 mb-2\">{algorithm.name}</h4>\n              <p className=\"text-sm text-gray-600 mb-3\">{algorithm.description}</p>\n              <div className=\"flex flex-wrap gap-1\">\n                {algorithm.capabilities.slice(0, 2).map((capability: string) => (\n                  <span\n                    key={capability}\n                    className=\"px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded\"\n                  >\n                    {capability.replace('_', ' ')}\n                  </span>\n                ))}\n                {algorithm.capabilities.length > 2 && (\n                  <span className=\"px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded\">\n                    +{algorithm.capabilities.length - 2} more\n                  </span>\n                )}\n              </div>\n            </div>\n          ))}\n        </div>\n        <div className=\"mt-6 text-center\">\n          <button\n            onClick={() => onViewChange('algorithms')}\n            className=\"bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors\"\n          >\n            View All Algorithms\n          </button>\n        </div>\n      </div>\n\n      {/* Getting Started */}\n      <div className=\"bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg shadow-sm p-6 text-white\">\n        <h3 className=\"text-xl font-semibold mb-4\">Getting Started</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div>\n            <h4 className=\"font-medium mb-2\">1. Choose Your Algorithm</h4>\n            <p className=\"text-indigo-100 text-sm\">\n              Browse our library of advanced algorithms including Bayesian forecasting, \n              econometric models, and AI agents.\n            </p>\n          </div>\n          <div>\n            <h4 className=\"font-medium mb-2\">2. Configure Parameters</h4>\n            <p className=\"text-indigo-100 text-sm\">\n              Adjust algorithm parameters in real-time with our interactive \n              configuration interface.\n            </p>\n          </div>\n          <div>\n            <h4 className=\"font-medium mb-2\">3. Analyze Results</h4>\n            <p className=\"text-indigo-100 text-sm\">\n              View interactive visualizations, confidence intervals, and \n              business insights from your analysis.\n            </p>\n          </div>\n          <div>\n            <h4 className=\"font-medium mb-2\">4. Export & Share</h4>\n            <p className=\"text-indigo-100 text-sm\">\n              Export results in multiple formats and share insights with \n              your team.\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;;;AAJA;;;AAYO,SAAS,UAAU,KAA+D;QAA/D,EAAE,UAAU,EAAE,iBAAiB,EAAE,YAAY,EAAkB,GAA/D;;IACxB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,IAAI,CAAC,YAAY;QACf,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BAAgB;;;;;;;;;;;IAGrC;IAEA,MAAM,kBAAkB,WAAW,WAAW;IAC9C,MAAM,aAAa,WAAW,UAAU;IAExC,uDAAuD;IACvD,MAAM,qBAA4B,EAAE;IACpC,KAAK,MAAM,CAAC,cAAc,mBAAmB,IAAI,OAAO,OAAO,CAAC,WAAW,UAAU,EAAG;QACtF,MAAM,mBAAmB,OAAO,OAAO,CAAC,oBAAoB,KAAK,CAAC,GAAG;QACrE,KAAK,MAAM,CAAC,cAAc,UAAU,IAAI,iBAAkB;YACxD,mBAAmB,IAAI,CAAC;gBACtB,KAAK;gBACL,UAAU;gBACV,GAAG,SAAS;YACd;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,6LAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAE/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,6LAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAE/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,6LAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOrC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;sDAAoC;;;;;;sDACnD,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;kCAI7C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;sDAAoC,WAAW,MAAM;;;;;;sDACpE,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;kCAI7C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;sDAAmC;;;;;;sDAClD,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;kCAI7C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;sDAAmC;;;;;;sDAClD,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO/C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,6LAAC;wCAAG,WAAU;kDAA4B;;;;;;kDAC1C,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAEvC,6LAAC;gCACC,SAAS;oCACP,qCAAqC;oCACrC,MAAM,kBAAkB,kBAAkB,CAAC,EAAE;oCAC7C,IAAI,iBAAiB;wCACnB,kBAAkB,iBAAiB,gBAAgB,QAAQ;oCAC7D;gCACF;gCACA,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,6LAAC;wCAAG,WAAU;kDAA4B;;;;;;kDAC1C,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAEvC,6LAAC;gCACC,SAAS;oCACP,0CAA0C;oCAC1C,MAAM,aAAa,CAAA,GAAA,sHAAA,CAAA,+BAA4B,AAAD;oCAC9C,QAAQ,GAAG,CAAC,0BAA0B;gCACxC;gCACA,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,6LAAC;wCAAG,WAAU;kDAA4B;;;;;;kDAC1C,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;0BAM3C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;kCACZ,mBAAmB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,0BACnC,6LAAC;gCAEC,WAAU;gCACV,SAAS,IAAM,kBAAkB,WAAW,UAAU,QAAQ;;kDAE9D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAY,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU,GAAG;;;;;;0DACzD,6LAAC;gDAAK,WAAW,AAAC,kCAAuE,OAAtC,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU,QAAQ;0DACpF,UAAU,QAAQ,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;kDAGrC,6LAAC;wCAAG,WAAU;kDAAkC,UAAU,IAAI;;;;;;kDAC9D,6LAAC;wCAAE,WAAU;kDAA8B,UAAU,WAAW;;;;;;kDAChE,6LAAC;wCAAI,WAAU;;4CACZ,UAAU,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,2BACvC,6LAAC;oDAEC,WAAU;8DAET,WAAW,OAAO,CAAC,KAAK;mDAHpB;;;;;4CAMR,UAAU,YAAY,CAAC,MAAM,GAAG,mBAC/B,6LAAC;gDAAK,WAAU;;oDAAsD;oDAClE,UAAU,YAAY,CAAC,MAAM,GAAG;oDAAE;;;;;;;;;;;;;;+BAvBrC,AAAC,GAAwB,OAAtB,UAAU,QAAQ,EAAC,KAAiB,OAAd,UAAU,GAAG;;;;;;;;;;kCA8BjD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,WAAU;sCACX;;;;;;;;;;;;;;;;;0BAOL,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmB;;;;;;kDACjC,6LAAC;wCAAE,WAAU;kDAA0B;;;;;;;;;;;;0CAKzC,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmB;;;;;;kDACjC,6LAAC;wCAAE,WAAU;kDAA0B;;;;;;;;;;;;0CAKzC,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmB;;;;;;kDACjC,6LAAC;wCAAE,WAAU;kDAA0B;;;;;;;;;;;;0CAKzC,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmB;;;;;;kDACjC,6LAAC;wCAAE,WAAU;kDAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnD;GA7NgB;KAAA", "debugId": null}}, {"offset": {"line": 2592, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/src/components/data-upload.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef } from 'react';\nimport { apiClient } from '@/lib/api';\n\ninterface DataUploadProps {\n  onUploadSuccess?: (data: any) => void;\n  onUploadError?: (error: string) => void;\n}\n\nexport function DataUpload({ onUploadSuccess, onUploadError }: DataUploadProps) {\n  const [isUploading, setIsUploading] = useState(false);\n  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);\n  const [dragActive, setDragActive] = useState(false);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  const dataTypes = [\n    { value: 'opex', label: 'OPEX 2024', description: 'Operating expenses data' },\n    { value: 'capex', label: 'CAPEX Planning', description: 'Capital expenditure planning' },\n    { value: 'revenue', label: 'Revenue Data', description: 'Revenue and sales data' },\n    { value: 'other', label: 'Other', description: 'Other financial data' },\n  ];\n\n  const handleFileUpload = async (file: File, dataType: string, description?: string) => {\n    try {\n      setIsUploading(true);\n\n      const formData = new FormData();\n      formData.append('file', file);\n      formData.append('data_type', dataType);\n      if (description) {\n        formData.append('description', description);\n      }\n\n      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/data/upload`, {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        throw new Error(`Upload failed: ${response.statusText}`);\n      }\n\n      const result = await response.json();\n\n      if (result.success) {\n        setUploadedFiles(prev => [...prev, result.data]);\n        onUploadSuccess?.(result.data);\n      } else {\n        throw new Error(result.message || 'Upload failed');\n      }\n\n    } catch (error: any) {\n      console.error('Upload error:', error);\n      onUploadError?.(error.message);\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  const handleDrag = (e: React.DragEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (e.type === 'dragenter' || e.type === 'dragover') {\n      setDragActive(true);\n    } else if (e.type === 'dragleave') {\n      setDragActive(false);\n    }\n  };\n\n  const handleDrop = (e: React.DragEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setDragActive(false);\n\n    if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n      const file = e.dataTransfer.files[0];\n      if (validateFile(file)) {\n        // For drag & drop, default to 'other' type - user can change later\n        handleFileUpload(file, 'other');\n      }\n    }\n  };\n\n  const validateFile = (file: File): boolean => {\n    const validTypes = ['.xlsx', '.xls', '.csv', '.json'];\n    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();\n\n    if (!validTypes.includes(fileExtension)) {\n      onUploadError?.('Please upload Excel (.xlsx, .xls), CSV, or JSON files only');\n      return false;\n    }\n\n    if (file.size > 50 * 1024 * 1024) { // 50MB limit\n      onUploadError?.('File size must be less than 50MB');\n      return false;\n    }\n\n    return true;\n  };\n\n  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {\n    if (e.target.files && e.target.files[0]) {\n      const file = e.target.files[0];\n      if (validateFile(file)) {\n        // Show file selection modal or handle upload\n        handleFileUpload(file, 'other');\n      }\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Upload Area */}\n      <div\n        className={`\n          border-2 border-dashed rounded-lg p-8 text-center transition-colors\n          ${dragActive \n            ? 'border-indigo-500 bg-indigo-50' \n            : 'border-gray-300 hover:border-gray-400'\n          }\n          ${isUploading ? 'opacity-50 pointer-events-none' : ''}\n        `}\n        onDragEnter={handleDrag}\n        onDragLeave={handleDrag}\n        onDragOver={handleDrag}\n        onDrop={handleDrop}\n      >\n        {isUploading ? (\n          <div className=\"space-y-4\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto\"></div>\n            <p className=\"text-gray-600\">Uploading and processing file...</p>\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            <div className=\"text-6xl\">📁</div>\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n                Upload Your Data Files\n              </h3>\n              <p className=\"text-gray-600 mb-4\">\n                Drag and drop your OPEX 2024, CAPEX planning, revenue data, or other financial files here\n              </p>\n              <button\n                onClick={() => fileInputRef.current?.click()}\n                className=\"bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors\"\n              >\n                Choose Files\n              </button>\n              <input\n                ref={fileInputRef}\n                type=\"file\"\n                accept=\".xlsx,.xls,.csv,.json\"\n                onChange={handleFileSelect}\n                className=\"hidden\"\n              />\n            </div>\n            <p className=\"text-sm text-gray-500\">\n              Supports Excel (.xlsx, .xls), CSV, and JSON files up to 50MB\n            </p>\n          </div>\n        )}\n      </div>\n\n      {/* Quick Upload Buttons */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n        {dataTypes.map((type) => (\n          <div key={type.value} className=\"bg-white border rounded-lg p-4\">\n            <h4 className=\"font-medium text-gray-900 mb-1\">{type.label}</h4>\n            <p className=\"text-sm text-gray-600 mb-3\">{type.description}</p>\n            <button\n              onClick={() => {\n                fileInputRef.current?.click();\n                // Store the selected type for when file is chosen\n                fileInputRef.current?.setAttribute('data-type', type.value);\n              }}\n              className=\"w-full bg-gray-100 text-gray-700 px-3 py-2 rounded hover:bg-gray-200 transition-colors text-sm\"\n            >\n              Upload {type.label}\n            </button>\n          </div>\n        ))}\n      </div>\n\n      {/* Uploaded Files List */}\n      {uploadedFiles.length > 0 && (\n        <div className=\"bg-white rounded-lg border p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n            📊 Uploaded Files ({uploadedFiles.length})\n          </h3>\n          <div className=\"space-y-4\">\n            {uploadedFiles.map((file, index) => (\n              <div key={index} className=\"border rounded-lg p-4\">\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center mb-2\">\n                      <span className=\"text-lg mr-2\">\n                        {file.data_type === 'opex' ? '💰' :\n                         file.data_type === 'capex' ? '🏗️' :\n                         file.data_type === 'revenue' ? '📈' : '📄'}\n                      </span>\n                      <h4 className=\"font-medium text-gray-900\">{file.filename}</h4>\n                      <span className=\"ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded\">\n                        {file.data_type.toUpperCase()}\n                      </span>\n                      {file.file_format === 'json' && (\n                        <span className=\"ml-2 px-2 py-1 text-xs bg-green-100 text-green-800 rounded\">\n                          JSON\n                        </span>\n                      )}\n                    </div>\n                    <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600\">\n                      <div>\n                        <span className=\"font-medium\">Rows:</span> {file.rows.toLocaleString()}\n                      </div>\n                      <div>\n                        <span className=\"font-medium\">Columns:</span> {file.columns}\n                      </div>\n                      <div>\n                        <span className=\"font-medium\">Uploaded:</span> {new Date(file.upload_timestamp).toLocaleDateString()}\n                      </div>\n                      <div>\n                        <span className=\"font-medium\">Status:</span> \n                        <span className=\"text-green-600 ml-1\">✅ Processed</span>\n                      </div>\n                    </div>\n                    \n                    {/* Initial Analysis Results */}\n                    {file.initial_analysis && (\n                      <div className=\"mt-3 p-3 bg-gray-50 rounded\">\n                        <h5 className=\"font-medium text-gray-900 mb-2\">📋 Initial Analysis</h5>\n                        {file.initial_analysis.insights && (\n                          <ul className=\"text-sm text-gray-600 space-y-1\">\n                            {file.initial_analysis.insights.map((insight: string, i: number) => (\n                              <li key={i}>• {insight}</li>\n                            ))}\n                          </ul>\n                        )}\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Help Section */}\n      <div className=\"bg-blue-50 rounded-lg p-6\">\n        <h3 className=\"text-lg font-semibold text-blue-900 mb-3\">💡 Data Upload Tips</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-blue-800\">\n          <div>\n            <h4 className=\"font-medium mb-2\">OPEX Data Should Include:</h4>\n            <ul className=\"space-y-1\">\n              <li>• Cost amounts and categories</li>\n              <li>• Department or business unit</li>\n              <li>• Time periods (monthly/quarterly)</li>\n              <li>• Cost centers or GL accounts</li>\n            </ul>\n          </div>\n          <div>\n            <h4 className=\"font-medium mb-2\">CAPEX Data Should Include:</h4>\n            <ul className=\"space-y-1\">\n              <li>• Project names and descriptions</li>\n              <li>• Investment amounts and timelines</li>\n              <li>• Project status and phases</li>\n              <li>• Expected ROI or business case</li>\n            </ul>\n          </div>\n          <div>\n            <h4 className=\"font-medium mb-2\">Supported File Formats:</h4>\n            <ul className=\"space-y-1\">\n              <li>• Excel files (.xlsx, .xls)</li>\n              <li>• CSV files (.csv)</li>\n              <li>• JSON files (.json)</li>\n              <li>• Maximum size: 50MB</li>\n            </ul>\n            <p className=\"mt-2 text-xs\">\n              JSON files are ideal for structured CAPEX project data with metadata.\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;AAkCsC;;AAhCtC;;;AAFA;;AAUO,SAAS,WAAW,KAAmD;QAAnD,EAAE,eAAe,EAAE,aAAa,EAAmB,GAAnD;;IACzB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC5D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,YAAY;QAChB;YAAE,OAAO;YAAQ,OAAO;YAAa,aAAa;QAA0B;QAC5E;YAAE,OAAO;YAAS,OAAO;YAAkB,aAAa;QAA+B;QACvF;YAAE,OAAO;YAAW,OAAO;YAAgB,aAAa;QAAyB;QACjF;YAAE,OAAO;YAAS,OAAO;YAAS,aAAa;QAAuB;KACvE;IAED,MAAM,mBAAmB,OAAO,MAAY,UAAkB;QAC5D,IAAI;YACF,eAAe;YAEf,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YACxB,SAAS,MAAM,CAAC,aAAa;YAC7B,IAAI,aAAa;gBACf,SAAS,MAAM,CAAC,eAAe;YACjC;YAEA,MAAM,WAAW,MAAM,MAAM,AAAC,GAAkC,kEAAA,qBAAmB;gBACjF,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,AAAC,kBAAqC,OAApB,SAAS,UAAU;YACvD;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,iBAAiB,CAAA,OAAQ;2BAAI;wBAAM,OAAO,IAAI;qBAAC;gBAC/C,4BAAA,sCAAA,gBAAkB,OAAO,IAAI;YAC/B,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;QAEF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,0BAAA,oCAAA,cAAgB,MAAM,OAAO;QAC/B,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,IAAI,EAAE,IAAI,KAAK,eAAe,EAAE,IAAI,KAAK,YAAY;YACnD,cAAc;QAChB,OAAO,IAAI,EAAE,IAAI,KAAK,aAAa;YACjC,cAAc;QAChB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc;QAEd,IAAI,EAAE,YAAY,CAAC,KAAK,IAAI,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE,EAAE;YACnD,MAAM,OAAO,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE;YACpC,IAAI,aAAa,OAAO;gBACtB,mEAAmE;gBACnE,iBAAiB,MAAM;YACzB;QACF;IACF;IAEA,MAAM,eAAe,CAAC;YAEQ;QAD5B,MAAM,aAAa;YAAC;YAAS;YAAQ;YAAQ;SAAQ;QACrD,MAAM,gBAAgB,QAAM,uBAAA,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,gBAAxB,2CAAA,qBAA4B,WAAW;QAEnE,IAAI,CAAC,WAAW,QAAQ,CAAC,gBAAgB;YACvC,0BAAA,oCAAA,cAAgB;YAChB,OAAO;QACT;QAEA,IAAI,KAAK,IAAI,GAAG,KAAK,OAAO,MAAM;YAChC,0BAAA,oCAAA,cAAgB;YAChB,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE;YACvC,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;YAC9B,IAAI,aAAa,OAAO;gBACtB,6CAA6C;gBAC7C,iBAAiB,MAAM;YACzB;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBACC,WAAW,AAAC,8FAMR,OAJA,aACE,mCACA,yCACH,gBACqD,OAApD,cAAc,mCAAmC,IAAG;gBAExD,aAAa;gBACb,aAAa;gBACb,YAAY;gBACZ,QAAQ;0BAEP,4BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;yCAG/B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAAW;;;;;;sCAC1B,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CAGvD,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,6LAAC;oCACC,SAAS;4CAAM;gDAAA,wBAAA,aAAa,OAAO,cAApB,4CAAA,sBAAsB,KAAK;;oCAC1C,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,KAAK;oCACL,MAAK;oCACL,QAAO;oCACP,UAAU;oCACV,WAAU;;;;;;;;;;;;sCAGd,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;0BAQ3C,6LAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,qBACd,6LAAC;wBAAqB,WAAU;;0CAC9B,6LAAC;gCAAG,WAAU;0CAAkC,KAAK,KAAK;;;;;;0CAC1D,6LAAC;gCAAE,WAAU;0CAA8B,KAAK,WAAW;;;;;;0CAC3D,6LAAC;gCACC,SAAS;wCACP,uBACA,kDAAkD;oCAClD;qCAFA,wBAAA,aAAa,OAAO,cAApB,4CAAA,sBAAsB,KAAK;qCAE3B,yBAAA,aAAa,OAAO,cAApB,6CAAA,uBAAsB,YAAY,CAAC,aAAa,KAAK,KAAK;gCAC5D;gCACA,WAAU;;oCACX;oCACS,KAAK,KAAK;;;;;;;;uBAXZ,KAAK,KAAK;;;;;;;;;;YAkBvB,cAAc,MAAM,GAAG,mBACtB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;4BAA2C;4BACnC,cAAc,MAAM;4BAAC;;;;;;;kCAE3C,6LAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,MAAM,sBACxB,6LAAC;gCAAgB,WAAU;0CACzB,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEACb,KAAK,SAAS,KAAK,SAAS,OAC5B,KAAK,SAAS,KAAK,UAAU,QAC7B,KAAK,SAAS,KAAK,YAAY,OAAO;;;;;;kEAEzC,6LAAC;wDAAG,WAAU;kEAA6B,KAAK,QAAQ;;;;;;kEACxD,6LAAC;wDAAK,WAAU;kEACb,KAAK,SAAS,CAAC,WAAW;;;;;;oDAE5B,KAAK,WAAW,KAAK,wBACpB,6LAAC;wDAAK,WAAU;kEAA6D;;;;;;;;;;;;0DAKjF,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAK,WAAU;0EAAc;;;;;;4DAAY;4DAAE,KAAK,IAAI,CAAC,cAAc;;;;;;;kEAEtE,6LAAC;;0EACC,6LAAC;gEAAK,WAAU;0EAAc;;;;;;4DAAe;4DAAE,KAAK,OAAO;;;;;;;kEAE7D,6LAAC;;0EACC,6LAAC;gEAAK,WAAU;0EAAc;;;;;;4DAAgB;4DAAE,IAAI,KAAK,KAAK,gBAAgB,EAAE,kBAAkB;;;;;;;kEAEpG,6LAAC;;0EACC,6LAAC;gEAAK,WAAU;0EAAc;;;;;;0EAC9B,6LAAC;gEAAK,WAAU;0EAAsB;;;;;;;;;;;;;;;;;;4CAKzC,KAAK,gBAAgB,kBACpB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAiC;;;;;;oDAC9C,KAAK,gBAAgB,CAAC,QAAQ,kBAC7B,6LAAC;wDAAG,WAAU;kEACX,KAAK,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAiB,kBACpD,6LAAC;;oEAAW;oEAAG;;+DAAN;;;;;;;;;;;;;;;;;;;;;;;;;;;+BA1Cf;;;;;;;;;;;;;;;;0BAyDlB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmB;;;;;;kDACjC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAGR,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmB;;;;;;kDACjC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAGR,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmB;;;;;;kDACjC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;kDAEN,6LAAC;wCAAE,WAAU;kDAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxC;GApRgB;KAAA", "debugId": null}}, {"offset": {"line": 3297, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/src/components/macro-dashboard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { apiClient } from '@/lib/api';\n\ninterface MacroDashboardProps {\n  onDataUpdate?: (data: any) => void;\n}\n\nexport function MacroDashboard({ onDataUpdate }: MacroDashboardProps) {\n  const [macroData, setMacroData] = useState<any>(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [selectedCountries, setSelectedCountries] = useState(['US', 'CL', 'CO', 'OM']);\n  const [selectedIndicators, setSelectedIndicators] = useState(['GDP', 'INFLATION', 'UNEMPLOYMENT']);\n  const [availableCountries, setAvailableCountries] = useState<string[]>([]);\n  const [availableIndicators, setAvailableIndicators] = useState<string[]>([]);\n\n  useEffect(() => {\n    loadAvailableOptions();\n  }, []);\n\n  const loadAvailableOptions = async () => {\n    try {\n      const [countriesResponse, indicatorsResponse] = await Promise.all([\n        fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/macro/countries`),\n        fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/macro/indicators/list`)\n      ]);\n\n      if (countriesResponse.ok) {\n        const countriesData = await countriesResponse.json();\n        setAvailableCountries(countriesData.countries || []);\n      }\n\n      if (indicatorsResponse.ok) {\n        const indicatorsData = await indicatorsResponse.json();\n        setAvailableIndicators(indicatorsData.indicators || []);\n      }\n    } catch (err) {\n      console.error('Error loading options:', err);\n    }\n  };\n\n  const loadMacroData = async () => {\n    try {\n      setIsLoading(true);\n      setError(null);\n\n      const params = new URLSearchParams({\n        countries: selectedCountries.join(','),\n        indicators: selectedIndicators.join(','),\n        years: '2022,2023,2024'\n      });\n\n      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/macro/indicators?${params}`);\n      \n      if (!response.ok) {\n        throw new Error(`Failed to fetch macro data: ${response.statusText}`);\n      }\n\n      const data = await response.json();\n      \n      if (data.success) {\n        setMacroData(data);\n        onDataUpdate?.(data);\n      } else {\n        throw new Error(data.message || 'Failed to load macro data');\n      }\n\n    } catch (err: any) {\n      setError(err.message);\n      console.error('Macro data error:', err);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const runMacroAnalysis = async () => {\n    try {\n      setIsLoading(true);\n\n      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/macro/analysis`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          countries: selectedCountries,\n          business_context: 'telecom',\n          analysis_type: 'comprehensive'\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error(`Analysis failed: ${response.statusText}`);\n      }\n\n      const data = await response.json();\n      \n      if (data.success) {\n        setMacroData(data);\n        onDataUpdate?.(data);\n      } else {\n        throw new Error(data.message || 'Analysis failed');\n      }\n\n    } catch (err: any) {\n      setError(err.message);\n      console.error('Macro analysis error:', err);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const countryFlags: { [key: string]: string } = {\n    'US': '🇺🇸',\n    'CL': '🇨🇱',\n    'CO': '🇨🇴',\n    'OM': '🇴🇲',\n    'GB': '🇬🇧',\n    'DE': '🇩🇪',\n    'FR': '🇫🇷',\n    'JP': '🇯🇵',\n    'CN': '🇨🇳',\n  };\n\n  const indicatorIcons: { [key: string]: string } = {\n    'GDP': '📈',\n    'INFLATION': '💰',\n    'UNEMPLOYMENT': '👥',\n    'INTEREST_RATE': '🏦',\n    'EXCHANGE_RATE': '💱',\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">🌍 Macro Economics Dashboard</h2>\n        <p className=\"text-gray-600\">\n          Monitor key economic indicators for your business markets and analyze their impact.\n        </p>\n      </div>\n\n      {/* Configuration */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">⚙️ Configuration</h3>\n        \n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {/* Countries Selection */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Countries\n            </label>\n            <div className=\"space-y-2\">\n              {availableCountries.slice(0, 8).map((country) => (\n                <label key={country} className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={selectedCountries.includes(country)}\n                    onChange={(e) => {\n                      if (e.target.checked) {\n                        setSelectedCountries([...selectedCountries, country]);\n                      } else {\n                        setSelectedCountries(selectedCountries.filter(c => c !== country));\n                      }\n                    }}\n                    className=\"mr-2\"\n                  />\n                  <span className=\"mr-2\">{countryFlags[country] || '🌍'}</span>\n                  <span>{country}</span>\n                </label>\n              ))}\n            </div>\n          </div>\n\n          {/* Indicators Selection */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Economic Indicators\n            </label>\n            <div className=\"space-y-2\">\n              {availableIndicators.slice(0, 6).map((indicator) => (\n                <label key={indicator} className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={selectedIndicators.includes(indicator)}\n                    onChange={(e) => {\n                      if (e.target.checked) {\n                        setSelectedIndicators([...selectedIndicators, indicator]);\n                      } else {\n                        setSelectedIndicators(selectedIndicators.filter(i => i !== indicator));\n                      }\n                    }}\n                    className=\"mr-2\"\n                  />\n                  <span className=\"mr-2\">{indicatorIcons[indicator] || '📊'}</span>\n                  <span>{indicator.replace('_', ' ')}</span>\n                </label>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        <div className=\"mt-6 flex gap-4\">\n          <button\n            onClick={loadMacroData}\n            disabled={isLoading || selectedCountries.length === 0 || selectedIndicators.length === 0}\n            className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:bg-gray-300 transition-colors\"\n          >\n            {isLoading ? 'Loading...' : '📊 Load Data'}\n          </button>\n          \n          <button\n            onClick={runMacroAnalysis}\n            disabled={isLoading || selectedCountries.length === 0}\n            className=\"bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 disabled:bg-gray-300 transition-colors\"\n          >\n            {isLoading ? 'Analyzing...' : '🔍 Run Analysis'}\n          </button>\n        </div>\n      </div>\n\n      {/* Error Display */}\n      {error && (\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n          <div className=\"flex items-center\">\n            <span className=\"text-red-600 text-xl mr-3\">❌</span>\n            <div>\n              <h4 className=\"font-medium text-red-800\">Error Loading Macro Data</h4>\n              <p className=\"text-red-700 text-sm\">{error}</p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Loading State */}\n      {isLoading && (\n        <div className=\"bg-white rounded-lg shadow-sm p-12 text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <h4 className=\"text-lg font-medium text-gray-900 mb-2\">Loading Macro Economic Data</h4>\n          <p className=\"text-gray-600\">Fetching data from external sources...</p>\n        </div>\n      )}\n\n      {/* Results Display */}\n      {macroData && !isLoading && (\n        <div className=\"space-y-6\">\n          {/* Summary Cards */}\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div className=\"bg-white rounded-lg shadow-sm p-6\">\n              <div className=\"flex items-center mb-2\">\n                <span className=\"text-2xl mr-3\">🌍</span>\n                <h4 className=\"font-semibold text-gray-900\">Countries</h4>\n              </div>\n              <div className=\"text-2xl font-bold text-blue-600\">{macroData.countries?.length || 0}</div>\n              <div className=\"text-sm text-gray-600\">Markets analyzed</div>\n            </div>\n\n            <div className=\"bg-white rounded-lg shadow-sm p-6\">\n              <div className=\"flex items-center mb-2\">\n                <span className=\"text-2xl mr-3\">📊</span>\n                <h4 className=\"font-semibold text-gray-900\">Indicators</h4>\n              </div>\n              <div className=\"text-2xl font-bold text-green-600\">{macroData.indicators?.length || 0}</div>\n              <div className=\"text-sm text-gray-600\">Economic metrics</div>\n            </div>\n\n            <div className=\"bg-white rounded-lg shadow-sm p-6\">\n              <div className=\"flex items-center mb-2\">\n                <span className=\"text-2xl mr-3\">📅</span>\n                <h4 className=\"font-semibold text-gray-900\">Time Period</h4>\n              </div>\n              <div className=\"text-2xl font-bold text-purple-600\">{macroData.years?.length || 0}</div>\n              <div className=\"text-sm text-gray-600\">Years of data</div>\n            </div>\n          </div>\n\n          {/* Business Analysis */}\n          {macroData.business_analysis && (\n            <div className=\"bg-white rounded-lg shadow-sm p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">💼 Business Impact Analysis</h3>\n              \n              <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n                {/* Key Insights */}\n                {macroData.business_analysis.key_insights && (\n                  <div>\n                    <h4 className=\"font-medium text-gray-900 mb-3\">💡 Key Insights</h4>\n                    <ul className=\"space-y-2 text-sm text-gray-600\">\n                      {macroData.business_analysis.key_insights.map((insight: string, index: number) => (\n                        <li key={index} className=\"flex items-start\">\n                          <span className=\"text-blue-500 mr-2\">•</span>\n                          {insight}\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                )}\n\n                {/* Risk Factors */}\n                {macroData.business_analysis.risk_factors && (\n                  <div>\n                    <h4 className=\"font-medium text-gray-900 mb-3\">⚠️ Risk Factors</h4>\n                    <ul className=\"space-y-2 text-sm text-gray-600\">\n                      {macroData.business_analysis.risk_factors.map((risk: string, index: number) => (\n                        <li key={index} className=\"flex items-start\">\n                          <span className=\"text-red-500 mr-2\">•</span>\n                          {risk}\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                )}\n\n                {/* Opportunities */}\n                {macroData.business_analysis.opportunities && (\n                  <div>\n                    <h4 className=\"font-medium text-gray-900 mb-3\">🚀 Opportunities</h4>\n                    <ul className=\"space-y-2 text-sm text-gray-600\">\n                      {macroData.business_analysis.opportunities.map((opportunity: string, index: number) => (\n                        <li key={index} className=\"flex items-start\">\n                          <span className=\"text-green-500 mr-2\">•</span>\n                          {opportunity}\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                )}\n              </div>\n\n              {/* Recommendations */}\n              {macroData.business_analysis.recommendations && (\n                <div className=\"mt-6 p-4 bg-blue-50 rounded-lg\">\n                  <h4 className=\"font-medium text-blue-900 mb-3\">📋 Strategic Recommendations</h4>\n                  <ul className=\"space-y-2 text-sm text-blue-800\">\n                    {macroData.business_analysis.recommendations.map((rec: string, index: number) => (\n                      <li key={index} className=\"flex items-start\">\n                        <span className=\"text-blue-600 mr-2\">→</span>\n                        {rec}\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n              )}\n            </div>\n          )}\n\n          {/* Raw Data Display */}\n          <div className=\"bg-white rounded-lg shadow-sm p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">📊 Economic Data</h3>\n            <div className=\"bg-gray-50 rounded-lg p-4 overflow-auto max-h-96\">\n              <pre className=\"text-sm text-gray-700\">\n                {JSON.stringify(macroData.data || macroData.macro_data, null, 2)}\n              </pre>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Empty State */}\n      {!macroData && !isLoading && !error && (\n        <div className=\"bg-white rounded-lg shadow-sm p-12 text-center\">\n          <div className=\"text-gray-400 text-6xl mb-4\">🌍</div>\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Ready to Load Macro Data</h3>\n          <p className=\"text-gray-600 mb-4\">\n            Select countries and indicators, then click \"Load Data\" to get started.\n          </p>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;AAyBiB;;AAvBjB;;;AAFA;;AASO,SAAS,eAAe,KAAqC;QAArC,EAAE,YAAY,EAAuB,GAArC;QAsPkC,sBASC,uBASC;;IAvQjE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAChD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAC;QAAM;QAAM;QAAM;KAAK;IACnF,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAC;QAAO;QAAa;KAAe;IACjG,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACzE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE3E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR;QACF;mCAAG,EAAE;IAEL,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,CAAC,mBAAmB,mBAAmB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAChE,MAAM,AAAC,GAAkC,kEAAA;gBACzC,MAAM,AAAC,GAAkC,kEAAA;aAC1C;YAED,IAAI,kBAAkB,EAAE,EAAE;gBACxB,MAAM,gBAAgB,MAAM,kBAAkB,IAAI;gBAClD,sBAAsB,cAAc,SAAS,IAAI,EAAE;YACrD;YAEA,IAAI,mBAAmB,EAAE,EAAE;gBACzB,MAAM,iBAAiB,MAAM,mBAAmB,IAAI;gBACpD,uBAAuB,eAAe,UAAU,IAAI,EAAE;YACxD;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,aAAa;YACb,SAAS;YAET,MAAM,SAAS,IAAI,gBAAgB;gBACjC,WAAW,kBAAkB,IAAI,CAAC;gBAClC,YAAY,mBAAmB,IAAI,CAAC;gBACpC,OAAO;YACT;YAEA,MAAM,WAAW,MAAM,MAAM,AAAC,GAA0D,kEAAxB,0BAA+B,OAAP;YAExF,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,AAAC,+BAAkD,OAApB,SAAS,UAAU;YACpE;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,aAAa;gBACb,yBAAA,mCAAA,aAAe;YACjB,OAAO;gBACL,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;YAClC;QAEF,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO;YACpB,QAAQ,KAAK,CAAC,qBAAqB;QACrC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI;YACF,aAAa;YAEb,MAAM,WAAW,MAAM,MAAM,AAAC,GAAkC,kEAAA,wBAAsB;gBACpF,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,WAAW;oBACX,kBAAkB;oBAClB,eAAe;gBACjB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,AAAC,oBAAuC,OAApB,SAAS,UAAU;YACzD;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,aAAa;gBACb,yBAAA,mCAAA,aAAe;YACjB,OAAO;gBACL,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;YAClC;QAEF,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO;YACpB,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAA0C;QAC9C,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;IACR;IAEA,MAAM,iBAA4C;QAChD,OAAO;QACP,aAAa;QACb,gBAAgB;QAChB,iBAAiB;QACjB,iBAAiB;IACnB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAM/B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAEzD,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCAAI,WAAU;kDACZ,mBAAmB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,wBACnC,6LAAC;gDAAoB,WAAU;;kEAC7B,6LAAC;wDACC,MAAK;wDACL,SAAS,kBAAkB,QAAQ,CAAC;wDACpC,UAAU,CAAC;4DACT,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE;gEACpB,qBAAqB;uEAAI;oEAAmB;iEAAQ;4DACtD,OAAO;gEACL,qBAAqB,kBAAkB,MAAM,CAAC,CAAA,IAAK,MAAM;4DAC3D;wDACF;wDACA,WAAU;;;;;;kEAEZ,6LAAC;wDAAK,WAAU;kEAAQ,YAAY,CAAC,QAAQ,IAAI;;;;;;kEACjD,6LAAC;kEAAM;;;;;;;+CAdG;;;;;;;;;;;;;;;;0CAqBlB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCAAI,WAAU;kDACZ,oBAAoB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,0BACpC,6LAAC;gDAAsB,WAAU;;kEAC/B,6LAAC;wDACC,MAAK;wDACL,SAAS,mBAAmB,QAAQ,CAAC;wDACrC,UAAU,CAAC;4DACT,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE;gEACpB,sBAAsB;uEAAI;oEAAoB;iEAAU;4DAC1D,OAAO;gEACL,sBAAsB,mBAAmB,MAAM,CAAC,CAAA,IAAK,MAAM;4DAC7D;wDACF;wDACA,WAAU;;;;;;kEAEZ,6LAAC;wDAAK,WAAU;kEAAQ,cAAc,CAAC,UAAU,IAAI;;;;;;kEACrD,6LAAC;kEAAM,UAAU,OAAO,CAAC,KAAK;;;;;;;+CAdpB;;;;;;;;;;;;;;;;;;;;;;kCAqBpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,UAAU,aAAa,kBAAkB,MAAM,KAAK,KAAK,mBAAmB,MAAM,KAAK;gCACvF,WAAU;0CAET,YAAY,eAAe;;;;;;0CAG9B,6LAAC;gCACC,SAAS;gCACT,UAAU,aAAa,kBAAkB,MAAM,KAAK;gCACpD,WAAU;0CAET,YAAY,iBAAiB;;;;;;;;;;;;;;;;;;YAMnC,uBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAU;sCAA4B;;;;;;sCAC5C,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA2B;;;;;;8CACzC,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;YAO5C,2BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;YAKhC,aAAa,CAAC,2BACb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAG,WAAU;0DAA8B;;;;;;;;;;;;kDAE9C,6LAAC;wCAAI,WAAU;kDAAoC,EAAA,uBAAA,UAAU,SAAS,cAAnB,2CAAA,qBAAqB,MAAM,KAAI;;;;;;kDAClF,6LAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;0CAGzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAG,WAAU;0DAA8B;;;;;;;;;;;;kDAE9C,6LAAC;wCAAI,WAAU;kDAAqC,EAAA,wBAAA,UAAU,UAAU,cAApB,4CAAA,sBAAsB,MAAM,KAAI;;;;;;kDACpF,6LAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;0CAGzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAG,WAAU;0DAA8B;;;;;;;;;;;;kDAE9C,6LAAC;wCAAI,WAAU;kDAAsC,EAAA,mBAAA,UAAU,KAAK,cAAf,uCAAA,iBAAiB,MAAM,KAAI;;;;;;kDAChF,6LAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;oBAK1C,UAAU,iBAAiB,kBAC1B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAEzD,6LAAC;gCAAI,WAAU;;oCAEZ,UAAU,iBAAiB,CAAC,YAAY,kBACvC,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAiC;;;;;;0DAC/C,6LAAC;gDAAG,WAAU;0DACX,UAAU,iBAAiB,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,SAAiB,sBAC9D,6LAAC;wDAAe,WAAU;;0EACxB,6LAAC;gEAAK,WAAU;0EAAqB;;;;;;4DACpC;;uDAFM;;;;;;;;;;;;;;;;oCAUhB,UAAU,iBAAiB,CAAC,YAAY,kBACvC,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAiC;;;;;;0DAC/C,6LAAC;gDAAG,WAAU;0DACX,UAAU,iBAAiB,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,MAAc,sBAC3D,6LAAC;wDAAe,WAAU;;0EACxB,6LAAC;gEAAK,WAAU;0EAAoB;;;;;;4DACnC;;uDAFM;;;;;;;;;;;;;;;;oCAUhB,UAAU,iBAAiB,CAAC,aAAa,kBACxC,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAiC;;;;;;0DAC/C,6LAAC;gDAAG,WAAU;0DACX,UAAU,iBAAiB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,aAAqB,sBACnE,6LAAC;wDAAe,WAAU;;0EACxB,6LAAC;gEAAK,WAAU;0EAAsB;;;;;;4DACrC;;uDAFM;;;;;;;;;;;;;;;;;;;;;;4BAWlB,UAAU,iBAAiB,CAAC,eAAe,kBAC1C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAiC;;;;;;kDAC/C,6LAAC;wCAAG,WAAU;kDACX,UAAU,iBAAiB,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,KAAa,sBAC7D,6LAAC;gDAAe,WAAU;;kEACxB,6LAAC;wDAAK,WAAU;kEAAqB;;;;;;oDACpC;;+CAFM;;;;;;;;;;;;;;;;;;;;;;kCAYrB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACZ,KAAK,SAAS,CAAC,UAAU,IAAI,IAAI,UAAU,UAAU,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;YAQvE,CAAC,aAAa,CAAC,aAAa,CAAC,uBAC5B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAA8B;;;;;;kCAC7C,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;;AAO5C;GA1WgB;KAAA", "debugId": null}}, {"offset": {"line": 4165, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/src/components/data-management.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { DataUpload } from './data-upload';\nimport { MacroDashboard } from './macro-dashboard';\n\ninterface DataManagementProps {\n  onDataUpdate?: (data: any) => void;\n}\n\nexport function DataManagement({ onDataUpdate }: DataManagementProps) {\n  const [activeTab, setActiveTab] = useState<'upload' | 'macro'>('upload');\n  const [uploadedData, setUploadedData] = useState<any[]>([]);\n  const [macroData, setMacroData] = useState<any>(null);\n\n  const handleUploadSuccess = (data: any) => {\n    setUploadedData(prev => [...prev, data]);\n    onDataUpdate?.(data);\n  };\n\n  const handleUploadError = (error: string) => {\n    console.error('Upload error:', error);\n    // You could add a toast notification here\n  };\n\n  const handleMacroDataUpdate = (data: any) => {\n    setMacroData(data);\n    onDataUpdate?.(data);\n  };\n\n  const tabs = [\n    { id: 'upload', label: 'File Upload', icon: '📁', description: 'Upload OPEX, CAPEX, and other data files' },\n    { id: 'macro', label: 'Macro Economics', icon: '🌍', description: 'Monitor economic indicators and trends' },\n  ] as const;\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">📊 Data Management</h2>\n        <p className=\"text-gray-600\">\n          Upload your financial data files and monitor macro economic indicators for comprehensive analysis.\n        </p>\n      </div>\n\n      {/* Tab Navigation */}\n      <div className=\"bg-white rounded-lg shadow-sm\">\n        <div className=\"border-b border-gray-200\">\n          <nav className=\"flex space-x-8 px-6\">\n            {tabs.map((tab) => (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id)}\n                className={`\n                  flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors\n                  ${activeTab === tab.id\n                    ? 'border-indigo-500 text-indigo-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }\n                `}\n              >\n                <span className=\"mr-2 text-lg\">{tab.icon}</span>\n                {tab.label}\n              </button>\n            ))}\n          </nav>\n        </div>\n\n        {/* Tab Content */}\n        <div className=\"p-6\">\n          {activeTab === 'upload' && (\n            <div className=\"space-y-4\">\n              <div className=\"mb-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">📁 File Upload</h3>\n                <p className=\"text-gray-600\">\n                  Upload your OPEX 2024, CAPEX planning, revenue data, and other financial files for analysis.\n                </p>\n              </div>\n              <DataUpload\n                onUploadSuccess={handleUploadSuccess}\n                onUploadError={handleUploadError}\n              />\n            </div>\n          )}\n\n          {activeTab === 'macro' && (\n            <div className=\"space-y-4\">\n              <div className=\"mb-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">🌍 Macro Economics</h3>\n                <p className=\"text-gray-600\">\n                  Monitor key economic indicators for your business markets and analyze their impact on your operations.\n                </p>\n              </div>\n              <MacroDashboard onDataUpdate={handleMacroDataUpdate} />\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Data Summary */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        {/* Uploaded Files Summary */}\n        <div className=\"bg-white rounded-lg shadow-sm p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">📁 Uploaded Data</h3>\n          \n          {uploadedData.length > 0 ? (\n            <div className=\"space-y-3\">\n              {uploadedData.slice(0, 3).map((file, index) => (\n                <div key={index} className=\"flex items-center justify-between p-3 bg-gray-50 rounded\">\n                  <div className=\"flex items-center\">\n                    <span className=\"text-lg mr-3\">\n                      {file.data_type === 'opex' ? '💰' : \n                       file.data_type === 'capex' ? '🏗️' : \n                       file.data_type === 'revenue' ? '📈' : '📄'}\n                    </span>\n                    <div>\n                      <div className=\"font-medium text-gray-900\">{file.filename}</div>\n                      <div className=\"text-sm text-gray-600\">\n                        {file.rows.toLocaleString()} rows • {file.columns} columns\n                      </div>\n                    </div>\n                  </div>\n                  <span className=\"px-2 py-1 text-xs bg-green-100 text-green-800 rounded\">\n                    {file.data_type.toUpperCase()}\n                  </span>\n                </div>\n              ))}\n              \n              {uploadedData.length > 3 && (\n                <div className=\"text-center text-sm text-gray-600\">\n                  +{uploadedData.length - 3} more files\n                </div>\n              )}\n              \n              <button\n                onClick={() => setActiveTab('upload')}\n                className=\"w-full mt-3 text-indigo-600 hover:text-indigo-700 text-sm font-medium\"\n              >\n                View All Files →\n              </button>\n            </div>\n          ) : (\n            <div className=\"text-center py-8\">\n              <div className=\"text-gray-400 text-4xl mb-2\">📁</div>\n              <p className=\"text-gray-600 text-sm\">No files uploaded yet</p>\n              <button\n                onClick={() => setActiveTab('upload')}\n                className=\"mt-2 text-indigo-600 hover:text-indigo-700 text-sm font-medium\"\n              >\n                Upload Files →\n              </button>\n            </div>\n          )}\n        </div>\n\n        {/* Macro Data Summary */}\n        <div className=\"bg-white rounded-lg shadow-sm p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">🌍 Macro Economics</h3>\n          \n          {macroData ? (\n            <div className=\"space-y-3\">\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div className=\"text-center p-3 bg-blue-50 rounded\">\n                  <div className=\"text-2xl font-bold text-blue-600\">\n                    {macroData.countries?.length || 0}\n                  </div>\n                  <div className=\"text-sm text-blue-800\">Countries</div>\n                </div>\n                <div className=\"text-center p-3 bg-green-50 rounded\">\n                  <div className=\"text-2xl font-bold text-green-600\">\n                    {macroData.indicators?.length || 0}\n                  </div>\n                  <div className=\"text-sm text-green-800\">Indicators</div>\n                </div>\n              </div>\n              \n              {macroData.business_analysis?.key_insights && (\n                <div className=\"mt-4\">\n                  <h4 className=\"font-medium text-gray-900 mb-2\">💡 Latest Insights</h4>\n                  <ul className=\"text-sm text-gray-600 space-y-1\">\n                    {macroData.business_analysis.key_insights.slice(0, 2).map((insight: string, index: number) => (\n                      <li key={index} className=\"flex items-start\">\n                        <span className=\"text-blue-500 mr-2\">•</span>\n                        {insight}\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n              )}\n              \n              <button\n                onClick={() => setActiveTab('macro')}\n                className=\"w-full mt-3 text-indigo-600 hover:text-indigo-700 text-sm font-medium\"\n              >\n                View Full Analysis →\n              </button>\n            </div>\n          ) : (\n            <div className=\"text-center py-8\">\n              <div className=\"text-gray-400 text-4xl mb-2\">🌍</div>\n              <p className=\"text-gray-600 text-sm\">No macro data loaded yet</p>\n              <button\n                onClick={() => setActiveTab('macro')}\n                className=\"mt-2 text-indigo-600 hover:text-indigo-700 text-sm font-medium\"\n              >\n                Load Macro Data →\n              </button>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Integration Status */}\n      <div className=\"bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">🔗 Data Integration Status</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <div className=\"flex items-center\">\n            <div className={`w-3 h-3 rounded-full mr-3 ${uploadedData.length > 0 ? 'bg-green-500' : 'bg-gray-300'}`}></div>\n            <span className=\"text-sm\">\n              <span className=\"font-medium\">Financial Data:</span> {uploadedData.length > 0 ? 'Connected' : 'Not Connected'}\n            </span>\n          </div>\n          <div className=\"flex items-center\">\n            <div className={`w-3 h-3 rounded-full mr-3 ${macroData ? 'bg-green-500' : 'bg-gray-300'}`}></div>\n            <span className=\"text-sm\">\n              <span className=\"font-medium\">Macro Data:</span> {macroData ? 'Connected' : 'Not Connected'}\n            </span>\n          </div>\n          <div className=\"flex items-center\">\n            <div className={`w-3 h-3 rounded-full mr-3 ${uploadedData.length > 0 && macroData ? 'bg-green-500' : 'bg-gray-300'}`}></div>\n            <span className=\"text-sm\">\n              <span className=\"font-medium\">Analysis Ready:</span> {uploadedData.length > 0 && macroData ? 'Yes' : 'Partial'}\n            </span>\n          </div>\n        </div>\n        \n        {uploadedData.length > 0 && macroData && (\n          <div className=\"mt-4 p-3 bg-green-100 rounded-lg\">\n            <p className=\"text-green-800 text-sm\">\n              ✅ <strong>Ready for Advanced Analysis!</strong> You now have both financial data and macro economic indicators loaded. \n              Your algorithms can now provide more comprehensive insights by combining internal data with external economic factors.\n            </p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAUO,SAAS,eAAe,KAAqC;QAArC,EAAE,YAAY,EAAuB,GAArC;QA0JV,sBAMA,uBAMN;;IArKb,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAEhD,MAAM,sBAAsB,CAAC;QAC3B,gBAAgB,CAAA,OAAQ;mBAAI;gBAAM;aAAK;QACvC,yBAAA,mCAAA,aAAe;IACjB;IAEA,MAAM,oBAAoB,CAAC;QACzB,QAAQ,KAAK,CAAC,iBAAiB;IAC/B,0CAA0C;IAC5C;IAEA,MAAM,wBAAwB,CAAC;QAC7B,aAAa;QACb,yBAAA,mCAAA,aAAe;IACjB;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAU,OAAO;YAAe,MAAM;YAAM,aAAa;QAA2C;QAC1G;YAAE,IAAI;YAAS,OAAO;YAAmB,MAAM;YAAM,aAAa;QAAyC;KAC5G;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAM/B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,KAAK,GAAG,CAAC,CAAC,oBACT,6LAAC;oCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;oCAClC,WAAW,AAAC,uHAKT,OAHC,cAAc,IAAI,EAAE,GAClB,sCACA,8EACH;;sDAGH,6LAAC;4CAAK,WAAU;sDAAgB,IAAI,IAAI;;;;;;wCACvC,IAAI,KAAK;;mCAXL,IAAI,EAAE;;;;;;;;;;;;;;;kCAkBnB,6LAAC;wBAAI,WAAU;;4BACZ,cAAc,0BACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAI/B,6LAAC,uIAAA,CAAA,aAAU;wCACT,iBAAiB;wCACjB,eAAe;;;;;;;;;;;;4BAKpB,cAAc,yBACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAI/B,6LAAC,2IAAA,CAAA,iBAAc;wCAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;0BAOtC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;4BAExD,aAAa,MAAM,GAAG,kBACrB,6LAAC;gCAAI,WAAU;;oCACZ,aAAa,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,sBACnC,6LAAC;4CAAgB,WAAU;;8DACzB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEACb,KAAK,SAAS,KAAK,SAAS,OAC5B,KAAK,SAAS,KAAK,UAAU,QAC7B,KAAK,SAAS,KAAK,YAAY,OAAO;;;;;;sEAEzC,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;8EAA6B,KAAK,QAAQ;;;;;;8EACzD,6LAAC;oEAAI,WAAU;;wEACZ,KAAK,IAAI,CAAC,cAAc;wEAAG;wEAAS,KAAK,OAAO;wEAAC;;;;;;;;;;;;;;;;;;;8DAIxD,6LAAC;oDAAK,WAAU;8DACb,KAAK,SAAS,CAAC,WAAW;;;;;;;2CAfrB;;;;;oCAoBX,aAAa,MAAM,GAAG,mBACrB,6LAAC;wCAAI,WAAU;;4CAAoC;4CAC/C,aAAa,MAAM,GAAG;4CAAE;;;;;;;kDAI9B,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAU;kDACX;;;;;;;;;;;qDAKH,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAA8B;;;;;;kDAC7C,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;kDACrC,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAQP,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;4BAExD,0BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,EAAA,uBAAA,UAAU,SAAS,cAAnB,2CAAA,qBAAqB,MAAM,KAAI;;;;;;kEAElC,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,EAAA,wBAAA,UAAU,UAAU,cAApB,4CAAA,sBAAsB,MAAM,KAAI;;;;;;kEAEnC,6LAAC;wDAAI,WAAU;kEAAyB;;;;;;;;;;;;;;;;;;oCAI3C,EAAA,+BAAA,UAAU,iBAAiB,cAA3B,mDAAA,6BAA6B,YAAY,mBACxC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAiC;;;;;;0DAC/C,6LAAC;gDAAG,WAAU;0DACX,UAAU,iBAAiB,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAiB,sBAC1E,6LAAC;wDAAe,WAAU;;0EACxB,6LAAC;gEAAK,WAAU;0EAAqB;;;;;;4DACpC;;uDAFM;;;;;;;;;;;;;;;;kDASjB,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAU;kDACX;;;;;;;;;;;qDAKH,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAA8B;;;;;;kDAC7C,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;kDACrC,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAW,AAAC,6BAAqF,OAAzD,aAAa,MAAM,GAAG,IAAI,iBAAiB;;;;;;kDACxF,6LAAC;wCAAK,WAAU;;0DACd,6LAAC;gDAAK,WAAU;0DAAc;;;;;;4CAAsB;4CAAE,aAAa,MAAM,GAAG,IAAI,cAAc;;;;;;;;;;;;;0CAGlG,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAW,AAAC,6BAAuE,OAA3C,YAAY,iBAAiB;;;;;;kDAC1E,6LAAC;wCAAK,WAAU;;0DACd,6LAAC;gDAAK,WAAU;0DAAc;;;;;;4CAAkB;4CAAE,YAAY,cAAc;;;;;;;;;;;;;0CAGhF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAW,AAAC,6BAAkG,OAAtE,aAAa,MAAM,GAAG,KAAK,YAAY,iBAAiB;;;;;;kDACrG,6LAAC;wCAAK,WAAU;;0DACd,6LAAC;gDAAK,WAAU;0DAAc;;;;;;4CAAsB;4CAAE,aAAa,MAAM,GAAG,KAAK,YAAY,QAAQ;;;;;;;;;;;;;;;;;;;oBAK1G,aAAa,MAAM,GAAG,KAAK,2BAC1B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;gCAAyB;8CAClC,6LAAC;8CAAO;;;;;;gCAAqC;;;;;;;;;;;;;;;;;;;;;;;;AAQ7D;GA7OgB;KAAA", "debugId": null}}, {"offset": {"line": 4873, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/src/components/scenario-planning.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { apiClient } from '@/lib/api';\n\ninterface ScenarioPlanningProps {\n  onScenarioUpdate?: (scenario: any) => void;\n}\n\nexport function ScenarioPlanning({ onScenarioUpdate }: ScenarioPlanningProps) {\n  const [currentStep, setCurrentStep] = useState<'constraints' | 'targets' | 'risk' | 'simulation' | 'results'>('constraints');\n  const [constraints, setConstraints] = useState({\n    totalOpexLimit: 50000000,\n    totalCapexLimit: 25000000,\n    minCashReserve: 5000000,\n  });\n  const [targets, setTargets] = useState({\n    revenueGrowth: 0.05,\n    marketExpansion: 0.10,\n    costReduction: 0.03,\n  });\n  const [riskAppetite, setRiskAppetite] = useState<'low' | 'medium' | 'high'>('medium');\n  const [simulationResults, setSimulationResults] = useState<any>(null);\n  const [isRunning, setIsRunning] = useState(false);\n  const [savedScenarios, setSavedScenarios] = useState<any[]>([]);\n\n  const steps = [\n    { id: 'constraints', label: 'Define Constraints', icon: '⚙️', completed: true },\n    { id: 'targets', label: 'Set Growth Targets', icon: '🎯', completed: currentStep !== 'constraints' },\n    { id: 'risk', label: 'Risk Appetite', icon: '⚖️', completed: ['simulation', 'results'].includes(currentStep) },\n    { id: 'simulation', label: 'Run Simulation', icon: '🎲', completed: currentStep === 'results' },\n    { id: 'results', label: 'Explore Results', icon: '📊', completed: false },\n  ];\n\n  const runSimulation = async () => {\n    try {\n      setIsRunning(true);\n      \n      const response = await apiClient.runScenarioAgent({\n        base_assumptions: {\n          revenue_growth: targets.revenueGrowth,\n          market_expansion: targets.marketExpansion,\n          cost_reduction: targets.costReduction,\n        },\n        constraints: {\n          opex_limit: constraints.totalOpexLimit,\n          capex_limit: constraints.totalCapexLimit,\n          cash_reserve: constraints.minCashReserve,\n        },\n        risk_parameters: {\n          risk_appetite: riskAppetite,\n          economic_volatility: riskAppetite === 'low' ? 0.1 : riskAppetite === 'medium' ? 0.2 : 0.3,\n          market_uncertainty: 0.15,\n        },\n        simulation_count: 1000,\n      });\n\n      if (response.success) {\n        setSimulationResults(response.data);\n        setCurrentStep('results');\n        onScenarioUpdate?.(response.data);\n      }\n    } catch (error) {\n      console.error('Simulation error:', error);\n    } finally {\n      setIsRunning(false);\n    }\n  };\n\n  const saveScenario = () => {\n    const scenario = {\n      id: Date.now(),\n      name: `Scenario ${savedScenarios.length + 1}`,\n      constraints,\n      targets,\n      riskAppetite,\n      results: simulationResults,\n      createdAt: new Date().toISOString(),\n    };\n    setSavedScenarios([...savedScenarios, scenario]);\n  };\n\n  const renderStepContent = () => {\n    switch (currentStep) {\n      case 'constraints':\n        return (\n          <div className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Define Financial Constraints</h3>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Total OPEX Limit\n                </label>\n                <div className=\"relative\">\n                  <span className=\"absolute left-3 top-2 text-gray-500\">$</span>\n                  <input\n                    type=\"number\"\n                    value={constraints.totalOpexLimit}\n                    onChange={(e) => setConstraints({...constraints, totalOpexLimit: Number(e.target.value)})}\n                    className=\"pl-8 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n                  />\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Total CAPEX Limit\n                </label>\n                <div className=\"relative\">\n                  <span className=\"absolute left-3 top-2 text-gray-500\">$</span>\n                  <input\n                    type=\"number\"\n                    value={constraints.totalCapexLimit}\n                    onChange={(e) => setConstraints({...constraints, totalCapexLimit: Number(e.target.value)})}\n                    className=\"pl-8 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n                  />\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Minimum Cash Reserve\n                </label>\n                <div className=\"relative\">\n                  <span className=\"absolute left-3 top-2 text-gray-500\">$</span>\n                  <input\n                    type=\"number\"\n                    value={constraints.minCashReserve}\n                    onChange={(e) => setConstraints({...constraints, minCashReserve: Number(e.target.value)})}\n                    className=\"pl-8 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n                  />\n                </div>\n              </div>\n            </div>\n\n            <button\n              onClick={() => setCurrentStep('targets')}\n              className=\"bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors\"\n            >\n              Next: Set Growth Targets →\n            </button>\n          </div>\n        );\n\n      case 'targets':\n        return (\n          <div className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Set Growth Targets</h3>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Revenue Growth Target\n                </label>\n                <div className=\"relative\">\n                  <input\n                    type=\"number\"\n                    step=\"0.01\"\n                    min=\"0\"\n                    max=\"1\"\n                    value={targets.revenueGrowth}\n                    onChange={(e) => setTargets({...targets, revenueGrowth: Number(e.target.value)})}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n                  />\n                  <span className=\"absolute right-3 top-2 text-gray-500\">%</span>\n                </div>\n                <p className=\"text-sm text-gray-600 mt-1\">{(targets.revenueGrowth * 100).toFixed(1)}% annual growth</p>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Market Expansion\n                </label>\n                <div className=\"relative\">\n                  <input\n                    type=\"number\"\n                    step=\"0.01\"\n                    min=\"0\"\n                    max=\"1\"\n                    value={targets.marketExpansion}\n                    onChange={(e) => setTargets({...targets, marketExpansion: Number(e.target.value)})}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n                  />\n                  <span className=\"absolute right-3 top-2 text-gray-500\">%</span>\n                </div>\n                <p className=\"text-sm text-gray-600 mt-1\">{(targets.marketExpansion * 100).toFixed(1)}% market growth</p>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Cost Reduction Target\n                </label>\n                <div className=\"relative\">\n                  <input\n                    type=\"number\"\n                    step=\"0.01\"\n                    min=\"0\"\n                    max=\"1\"\n                    value={targets.costReduction}\n                    onChange={(e) => setTargets({...targets, costReduction: Number(e.target.value)})}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n                  />\n                  <span className=\"absolute right-3 top-2 text-gray-500\">%</span>\n                </div>\n                <p className=\"text-sm text-gray-600 mt-1\">{(targets.costReduction * 100).toFixed(1)}% cost reduction</p>\n              </div>\n            </div>\n\n            <div className=\"flex gap-4\">\n              <button\n                onClick={() => setCurrentStep('constraints')}\n                className=\"bg-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-400 transition-colors\"\n              >\n                ← Back\n              </button>\n              <button\n                onClick={() => setCurrentStep('risk')}\n                className=\"bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors\"\n              >\n                Next: Risk Appetite →\n              </button>\n            </div>\n          </div>\n        );\n\n      case 'risk':\n        return (\n          <div className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Select Risk Appetite</h3>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              {(['low', 'medium', 'high'] as const).map((level) => (\n                <div\n                  key={level}\n                  className={`p-6 border-2 rounded-lg cursor-pointer transition-colors ${\n                    riskAppetite === level\n                      ? 'border-indigo-500 bg-indigo-50'\n                      : 'border-gray-200 hover:border-gray-300'\n                  }`}\n                  onClick={() => setRiskAppetite(level)}\n                >\n                  <div className=\"text-center\">\n                    <div className=\"text-3xl mb-2\">\n                      {level === 'low' ? '🛡️' : level === 'medium' ? '⚖️' : '🚀'}\n                    </div>\n                    <h4 className=\"font-semibold text-gray-900 capitalize\">{level} Risk</h4>\n                    <p className=\"text-sm text-gray-600 mt-2\">\n                      {level === 'low' && 'Conservative approach, prioritize stability'}\n                      {level === 'medium' && 'Balanced risk-reward optimization'}\n                      {level === 'high' && 'Aggressive growth, higher volatility'}\n                    </p>\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            <div className=\"flex gap-4\">\n              <button\n                onClick={() => setCurrentStep('targets')}\n                className=\"bg-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-400 transition-colors\"\n              >\n                ← Back\n              </button>\n              <button\n                onClick={() => setCurrentStep('simulation')}\n                className=\"bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors\"\n              >\n                Next: Run Simulation →\n              </button>\n            </div>\n          </div>\n        );\n\n      case 'simulation':\n        return (\n          <div className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Run Monte Carlo Simulation</h3>\n            \n            <div className=\"bg-gray-50 rounded-lg p-6\">\n              <h4 className=\"font-medium text-gray-900 mb-4\">Simulation Parameters</h4>\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\">\n                <div>\n                  <span className=\"font-medium\">OPEX Limit:</span>\n                  <div>${constraints.totalOpexLimit.toLocaleString()}</div>\n                </div>\n                <div>\n                  <span className=\"font-medium\">CAPEX Limit:</span>\n                  <div>${constraints.totalCapexLimit.toLocaleString()}</div>\n                </div>\n                <div>\n                  <span className=\"font-medium\">Revenue Growth:</span>\n                  <div>{(targets.revenueGrowth * 100).toFixed(1)}%</div>\n                </div>\n                <div>\n                  <span className=\"font-medium\">Risk Level:</span>\n                  <div className=\"capitalize\">{riskAppetite}</div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex gap-4\">\n              <button\n                onClick={() => setCurrentStep('risk')}\n                className=\"bg-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-400 transition-colors\"\n              >\n                ← Back\n              </button>\n              <button\n                onClick={runSimulation}\n                disabled={isRunning}\n                className=\"bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 disabled:bg-gray-400 transition-colors\"\n              >\n                {isRunning ? '🎲 Running Simulation...' : '🎲 Run Monte Carlo Simulation'}\n              </button>\n            </div>\n          </div>\n        );\n\n      case 'results':\n        return (\n          <div className=\"space-y-6\">\n            <div className=\"flex justify-between items-center\">\n              <h3 className=\"text-lg font-semibold text-gray-900\">Simulation Results</h3>\n              <button\n                onClick={saveScenario}\n                className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n              >\n                💾 Save Scenario\n              </button>\n            </div>\n            \n            {simulationResults && (\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                <div className=\"bg-white border rounded-lg p-6\">\n                  <h4 className=\"font-medium text-gray-900 mb-2\">📊 Probability Distribution</h4>\n                  <p className=\"text-sm text-gray-600\">Monte Carlo results with confidence intervals</p>\n                  {/* Placeholder for probability chart */}\n                  <div className=\"mt-4 h-32 bg-gray-100 rounded flex items-center justify-center\">\n                    <span className=\"text-gray-500\">Probability Chart</span>\n                  </div>\n                </div>\n\n                <div className=\"bg-white border rounded-lg p-6\">\n                  <h4 className=\"font-medium text-gray-900 mb-2\">🔥 Risk Heatmap</h4>\n                  <p className=\"text-sm text-gray-600\">Risk levels by domain/country</p>\n                  <div className=\"mt-4 h-32 bg-gray-100 rounded flex items-center justify-center\">\n                    <span className=\"text-gray-500\">Risk Heatmap</span>\n                  </div>\n                </div>\n\n                <div className=\"bg-white border rounded-lg p-6\">\n                  <h4 className=\"font-medium text-gray-900 mb-2\">🎚️ Sensitivity Analysis</h4>\n                  <p className=\"text-sm text-gray-600\">Impact of parameter changes</p>\n                  <div className=\"mt-4 h-32 bg-gray-100 rounded flex items-center justify-center\">\n                    <span className=\"text-gray-500\">Sensitivity Sliders</span>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            <div className=\"bg-gray-50 rounded-lg p-4\">\n              <pre className=\"text-sm text-gray-700 overflow-auto max-h-64\">\n                {JSON.stringify(simulationResults, null, 2)}\n              </pre>\n            </div>\n          </div>\n        );\n\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">🎯 Scenario Planning</h2>\n        <p className=\"text-gray-600\">\n          Define constraints, set growth targets, and run Monte Carlo simulations to explore possible outcomes.\n        </p>\n      </div>\n\n      {/* Progress Steps */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <div className=\"flex items-center justify-between mb-8\">\n          {steps.map((step, index) => (\n            <div key={step.id} className=\"flex items-center\">\n              <div\n                className={`\n                  flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors cursor-pointer\n                  ${step.completed || currentStep === step.id\n                    ? 'bg-indigo-600 border-indigo-600 text-white'\n                    : 'bg-white border-gray-300 text-gray-400'\n                  }\n                `}\n                onClick={() => setCurrentStep(step.id as any)}\n              >\n                {step.completed ? '✓' : step.icon}\n              </div>\n              <div className=\"ml-3 hidden md:block\">\n                <div className={`text-sm font-medium ${\n                  step.completed || currentStep === step.id ? 'text-indigo-600' : 'text-gray-400'\n                }`}>\n                  {step.label}\n                </div>\n              </div>\n              {index < steps.length - 1 && (\n                <div className={`w-12 h-0.5 mx-4 ${\n                  step.completed ? 'bg-indigo-600' : 'bg-gray-300'\n                }`} />\n              )}\n            </div>\n          ))}\n        </div>\n\n        {/* Step Content */}\n        {renderStepContent()}\n      </div>\n\n      {/* Saved Scenarios */}\n      {savedScenarios.length > 0 && (\n        <div className=\"bg-white rounded-lg shadow-sm p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">💾 Saved Scenarios ({savedScenarios.length})</h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n            {savedScenarios.map((scenario) => (\n              <div key={scenario.id} className=\"border rounded-lg p-4\">\n                <h4 className=\"font-medium text-gray-900\">{scenario.name}</h4>\n                <p className=\"text-sm text-gray-600 mt-1\">\n                  Risk: {scenario.riskAppetite} | Growth: {(scenario.targets.revenueGrowth * 100).toFixed(1)}%\n                </p>\n                <p className=\"text-xs text-gray-500 mt-2\">\n                  {new Date(scenario.createdAt).toLocaleDateString()}\n                </p>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AASO,SAAS,iBAAiB,KAA2C;QAA3C,EAAE,gBAAgB,EAAyB,GAA3C;;IAC/B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiE;IAC9G,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC7C,gBAAgB;QAChB,iBAAiB;QACjB,gBAAgB;IAClB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,eAAe;QACf,iBAAiB;QACjB,eAAe;IACjB;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;IAC5E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAChE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAE9D,MAAM,QAAQ;QACZ;YAAE,IAAI;YAAe,OAAO;YAAsB,MAAM;YAAM,WAAW;QAAK;QAC9E;YAAE,IAAI;YAAW,OAAO;YAAsB,MAAM;YAAM,WAAW,gBAAgB;QAAc;QACnG;YAAE,IAAI;YAAQ,OAAO;YAAiB,MAAM;YAAM,WAAW;gBAAC;gBAAc;aAAU,CAAC,QAAQ,CAAC;QAAa;QAC7G;YAAE,IAAI;YAAc,OAAO;YAAkB,MAAM;YAAM,WAAW,gBAAgB;QAAU;QAC9F;YAAE,IAAI;YAAW,OAAO;YAAmB,MAAM;YAAM,WAAW;QAAM;KACzE;IAED,MAAM,gBAAgB;QACpB,IAAI;YACF,aAAa;YAEb,MAAM,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC;gBAChD,kBAAkB;oBAChB,gBAAgB,QAAQ,aAAa;oBACrC,kBAAkB,QAAQ,eAAe;oBACzC,gBAAgB,QAAQ,aAAa;gBACvC;gBACA,aAAa;oBACX,YAAY,YAAY,cAAc;oBACtC,aAAa,YAAY,eAAe;oBACxC,cAAc,YAAY,cAAc;gBAC1C;gBACA,iBAAiB;oBACf,eAAe;oBACf,qBAAqB,iBAAiB,QAAQ,MAAM,iBAAiB,WAAW,MAAM;oBACtF,oBAAoB;gBACtB;gBACA,kBAAkB;YACpB;YAEA,IAAI,SAAS,OAAO,EAAE;gBACpB,qBAAqB,SAAS,IAAI;gBAClC,eAAe;gBACf,6BAAA,uCAAA,iBAAmB,SAAS,IAAI;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;QACrC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,WAAW;YACf,IAAI,KAAK,GAAG;YACZ,MAAM,AAAC,YAAqC,OAA1B,eAAe,MAAM,GAAG;YAC1C;YACA;YACA;YACA,SAAS;YACT,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,kBAAkB;eAAI;YAAgB;SAAS;IACjD;IAEA,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAEpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAsC;;;;;;8DACtD,6LAAC;oDACC,MAAK;oDACL,OAAO,YAAY,cAAc;oDACjC,UAAU,CAAC,IAAM,eAAe;4DAAC,GAAG,WAAW;4DAAE,gBAAgB,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACvF,WAAU;;;;;;;;;;;;;;;;;;8CAKhB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAsC;;;;;;8DACtD,6LAAC;oDACC,MAAK;oDACL,OAAO,YAAY,eAAe;oDAClC,UAAU,CAAC,IAAM,eAAe;4DAAC,GAAG,WAAW;4DAAE,iBAAiB,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACxF,WAAU;;;;;;;;;;;;;;;;;;8CAKhB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAsC;;;;;;8DACtD,6LAAC;oDACC,MAAK;oDACL,OAAO,YAAY,cAAc;oDACjC,UAAU,CAAC,IAAM,eAAe;4DAAC,GAAG,WAAW;4DAAE,gBAAgB,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACvF,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAMlB,6LAAC;4BACC,SAAS,IAAM,eAAe;4BAC9B,WAAU;sCACX;;;;;;;;;;;;YAMP,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAEpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,MAAK;oDACL,KAAI;oDACJ,KAAI;oDACJ,OAAO,QAAQ,aAAa;oDAC5B,UAAU,CAAC,IAAM,WAAW;4DAAC,GAAG,OAAO;4DAAE,eAAe,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAC;oDAC9E,WAAU;;;;;;8DAEZ,6LAAC;oDAAK,WAAU;8DAAuC;;;;;;;;;;;;sDAEzD,6LAAC;4CAAE,WAAU;;gDAA8B,CAAC,QAAQ,aAAa,GAAG,GAAG,EAAE,OAAO,CAAC;gDAAG;;;;;;;;;;;;;8CAGtF,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,MAAK;oDACL,KAAI;oDACJ,KAAI;oDACJ,OAAO,QAAQ,eAAe;oDAC9B,UAAU,CAAC,IAAM,WAAW;4DAAC,GAAG,OAAO;4DAAE,iBAAiB,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAC;oDAChF,WAAU;;;;;;8DAEZ,6LAAC;oDAAK,WAAU;8DAAuC;;;;;;;;;;;;sDAEzD,6LAAC;4CAAE,WAAU;;gDAA8B,CAAC,QAAQ,eAAe,GAAG,GAAG,EAAE,OAAO,CAAC;gDAAG;;;;;;;;;;;;;8CAGxF,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,MAAK;oDACL,KAAI;oDACJ,KAAI;oDACJ,OAAO,QAAQ,aAAa;oDAC5B,UAAU,CAAC,IAAM,WAAW;4DAAC,GAAG,OAAO;4DAAE,eAAe,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAC;oDAC9E,WAAU;;;;;;8DAEZ,6LAAC;oDAAK,WAAU;8DAAuC;;;;;;;;;;;;sDAEzD,6LAAC;4CAAE,WAAU;;gDAA8B,CAAC,QAAQ,aAAa,GAAG,GAAG,EAAE,OAAO,CAAC;gDAAG;;;;;;;;;;;;;;;;;;;sCAIxF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CACX;;;;;;;;;;;;;;;;;;YAOT,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAEpD,6LAAC;4BAAI,WAAU;sCACZ,AAAC;gCAAC;gCAAO;gCAAU;6BAAO,CAAW,GAAG,CAAC,CAAC,sBACzC,6LAAC;oCAEC,WAAW,AAAC,4DAIX,OAHC,iBAAiB,QACb,mCACA;oCAEN,SAAS,IAAM,gBAAgB;8CAE/B,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,UAAU,QAAQ,QAAQ,UAAU,WAAW,OAAO;;;;;;0DAEzD,6LAAC;gDAAG,WAAU;;oDAA0C;oDAAM;;;;;;;0DAC9D,6LAAC;gDAAE,WAAU;;oDACV,UAAU,SAAS;oDACnB,UAAU,YAAY;oDACtB,UAAU,UAAU;;;;;;;;;;;;;mCAhBpB;;;;;;;;;;sCAuBX,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CACX;;;;;;;;;;;;;;;;;;YAOT,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAEpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAiC;;;;;;8CAC/C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAK,WAAU;8DAAc;;;;;;8DAC9B,6LAAC;;wDAAI;wDAAE,YAAY,cAAc,CAAC,cAAc;;;;;;;;;;;;;sDAElD,6LAAC;;8DACC,6LAAC;oDAAK,WAAU;8DAAc;;;;;;8DAC9B,6LAAC;;wDAAI;wDAAE,YAAY,eAAe,CAAC,cAAc;;;;;;;;;;;;;sDAEnD,6LAAC;;8DACC,6LAAC;oDAAK,WAAU;8DAAc;;;;;;8DAC9B,6LAAC;;wDAAK,CAAC,QAAQ,aAAa,GAAG,GAAG,EAAE,OAAO,CAAC;wDAAG;;;;;;;;;;;;;sDAEjD,6LAAC;;8DACC,6LAAC;oDAAK,WAAU;8DAAc;;;;;;8DAC9B,6LAAC;oDAAI,WAAU;8DAAc;;;;;;;;;;;;;;;;;;;;;;;;sCAKnC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,YAAY,6BAA6B;;;;;;;;;;;;;;;;;;YAMpD,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;wBAKF,mCACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;sDAErC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;;;;;;8CAIpC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;sDACrC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;;;;;;8CAIpC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;sDACrC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;sCAMxC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ,KAAK,SAAS,CAAC,mBAAmB,MAAM;;;;;;;;;;;;;;;;;YAMnD;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAM/B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;gCAAkB,WAAU;;kDAC3B,6LAAC;wCACC,WAAW,AAAC,4IAKT,OAHC,KAAK,SAAS,IAAI,gBAAgB,KAAK,EAAE,GACvC,+CACA,0CACH;wCAEH,SAAS,IAAM,eAAe,KAAK,EAAE;kDAEpC,KAAK,SAAS,GAAG,MAAM,KAAK,IAAI;;;;;;kDAEnC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAW,AAAC,uBAEhB,OADC,KAAK,SAAS,IAAI,gBAAgB,KAAK,EAAE,GAAG,oBAAoB;sDAE/D,KAAK,KAAK;;;;;;;;;;;oCAGd,QAAQ,MAAM,MAAM,GAAG,mBACtB,6LAAC;wCAAI,WAAW,AAAC,mBAEhB,OADC,KAAK,SAAS,GAAG,kBAAkB;;;;;;;+BAtB/B,KAAK,EAAE;;;;;;;;;;oBA8BpB;;;;;;;YAIF,eAAe,MAAM,GAAG,mBACvB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;4BAA2C;4BAAqB,eAAe,MAAM;4BAAC;;;;;;;kCACpG,6LAAC;wBAAI,WAAU;kCACZ,eAAe,GAAG,CAAC,CAAC,yBACnB,6LAAC;gCAAsB,WAAU;;kDAC/B,6LAAC;wCAAG,WAAU;kDAA6B,SAAS,IAAI;;;;;;kDACxD,6LAAC;wCAAE,WAAU;;4CAA6B;4CACjC,SAAS,YAAY;4CAAC;4CAAY,CAAC,SAAS,OAAO,CAAC,aAAa,GAAG,GAAG,EAAE,OAAO,CAAC;4CAAG;;;;;;;kDAE7F,6LAAC;wCAAE,WAAU;kDACV,IAAI,KAAK,SAAS,SAAS,EAAE,kBAAkB;;;;;;;+BAN1C,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;AAenC;GAjbgB;KAAA", "debugId": null}}, {"offset": {"line": 6028, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/src/components/capex-optimization.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { apiClient } from '@/lib/api';\n\ninterface CapexOptimizationProps {\n  onOptimizationUpdate?: (data: any) => void;\n}\n\nexport function CapexOptimization({ onOptimizationUpdate }: CapexOptimizationProps) {\n  const [currentStep, setCurrentStep] = useState<'upload' | 'scoring' | 'weights' | 'optimize' | 'whatif'>('upload');\n  const [capexData, setCapexData] = useState<any[]>([]);\n  const [scoredProjects, setScoredProjects] = useState<any[]>([]);\n  const [weights, setWeights] = useState({\n    strategic_alignment: 0.3,\n    roi_potential: 0.4,\n    risk_level: 0.2,\n    implementation_ease: 0.1,\n  });\n  const [optimizationGoal, setOptimizationGoal] = useState<'maximize_roi' | 'minimize_risk' | 'balanced'>('balanced');\n  const [optimizedAllocation, setOptimizedAllocation] = useState<any>(null);\n  const [isProcessing, setIsProcessing] = useState(false);\n\n  const steps = [\n    { id: 'upload', label: 'Upload CAPEX Data', icon: '📁', completed: capexData.length > 0 },\n    { id: 'scoring', label: 'AI Scoring', icon: '🤖', completed: scoredProjects.length > 0 },\n    { id: 'weights', label: 'Set Weights', icon: '⚖️', completed: false },\n    { id: 'optimize', label: 'Optimize Allocation', icon: '🎯', completed: optimizedAllocation !== null },\n    { id: 'whatif', label: 'What-If Analysis', icon: '🔍', completed: false },\n  ];\n\n  const runAIScoring = async () => {\n    try {\n      setIsProcessing(true);\n      \n      // Simulate AI scoring for demo\n      const scored = capexData.map((project, index) => ({\n        ...project,\n        ai_score: Math.floor(Math.random() * 40) + 60, // 60-100 score\n        strategic_alignment: Math.floor(Math.random() * 30) + 70,\n        roi_potential: Math.floor(Math.random() * 35) + 65,\n        risk_level: Math.floor(Math.random() * 40) + 30, // Lower is better\n        implementation_ease: Math.floor(Math.random() * 25) + 75,\n        justification: [\n          'Strong alignment with digital transformation strategy',\n          'Positive ROI based on historical data and market trends',\n          'Moderate implementation risk with clear mitigation plan'\n        ]\n      }));\n      \n      setScoredProjects(scored);\n      setCurrentStep('weights');\n      \n    } catch (error) {\n      console.error('AI scoring error:', error);\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n\n  const runOptimization = async () => {\n    try {\n      setIsProcessing(true);\n      \n      // Calculate weighted scores\n      const weightedProjects = scoredProjects.map(project => ({\n        ...project,\n        weighted_score: (\n          project.strategic_alignment * weights.strategic_alignment +\n          project.roi_potential * weights.roi_potential +\n          (100 - project.risk_level) * weights.risk_level + // Invert risk\n          project.implementation_ease * weights.implementation_ease\n        )\n      }));\n\n      // Sort by weighted score\n      const optimized = weightedProjects.sort((a, b) => b.weighted_score - a.weighted_score);\n      \n      setOptimizedAllocation({\n        projects: optimized,\n        total_budget: optimized.reduce((sum, p) => sum + (p.budget || 0), 0),\n        expected_roi: optimized.reduce((sum, p) => sum + (p.roi_potential * (p.budget || 0) / 100), 0),\n        risk_score: optimized.reduce((sum, p) => sum + p.risk_level, 0) / optimized.length,\n      });\n      \n      setCurrentStep('whatif');\n      onOptimizationUpdate?.(optimized);\n      \n    } catch (error) {\n      console.error('Optimization error:', error);\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n\n  const renderStepContent = () => {\n    switch (currentStep) {\n      case 'upload':\n        return (\n          <div className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Upload CAPEX Data</h3>\n            \n            {capexData.length === 0 ? (\n              <div className=\"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center\">\n                <div className=\"text-4xl mb-4\">📁</div>\n                <h4 className=\"text-lg font-medium text-gray-900 mb-2\">Upload CAPEX Planning Data</h4>\n                <p className=\"text-gray-600 mb-4\">\n                  Upload your CAPEX planning spreadsheet with project details, budgets, and timelines.\n                </p>\n                <button\n                  onClick={() => {\n                    // Simulate data upload for demo\n                    const sampleData = [\n                      { id: 1, name: '5G Network Expansion', budget: 15000000, category: 'Infrastructure', priority: 'High' },\n                      { id: 2, name: 'Digital Platform Upgrade', budget: 8000000, category: 'Technology', priority: 'Medium' },\n                      { id: 3, name: 'Customer Experience Center', budget: 3000000, category: 'Customer', priority: 'Low' },\n                      { id: 4, name: 'Data Analytics Platform', budget: 5000000, category: 'Analytics', priority: 'High' },\n                      { id: 5, name: 'Security Infrastructure', budget: 4000000, category: 'Security', priority: 'Medium' },\n                    ];\n                    setCapexData(sampleData);\n                  }}\n                  className=\"bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors\"\n                >\n                  📁 Upload Sample CAPEX Data\n                </button>\n              </div>\n            ) : (\n              <div className=\"space-y-4\">\n                <div className=\"bg-green-50 border border-green-200 rounded-lg p-4\">\n                  <h4 className=\"font-medium text-green-800\">✅ CAPEX Data Loaded</h4>\n                  <p className=\"text-green-700 text-sm\">\n                    {capexData.length} projects loaded with total budget of ${capexData.reduce((sum, p) => sum + p.budget, 0).toLocaleString()}\n                  </p>\n                </div>\n                \n                <div className=\"overflow-x-auto\">\n                  <table className=\"min-w-full divide-y divide-gray-200\">\n                    <thead className=\"bg-gray-50\">\n                      <tr>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase\">Project</th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase\">Budget</th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase\">Category</th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase\">Priority</th>\n                      </tr>\n                    </thead>\n                    <tbody className=\"bg-white divide-y divide-gray-200\">\n                      {capexData.map((project) => (\n                        <tr key={project.id}>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                            {project.name}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                            ${project.budget.toLocaleString()}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                            {project.category}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap\">\n                            <span className={`px-2 py-1 text-xs rounded-full ${\n                              project.priority === 'High' ? 'bg-red-100 text-red-800' :\n                              project.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' :\n                              'bg-green-100 text-green-800'\n                            }`}>\n                              {project.priority}\n                            </span>\n                          </td>\n                        </tr>\n                      ))}\n                    </tbody>\n                  </table>\n                </div>\n                \n                <button\n                  onClick={() => setCurrentStep('scoring')}\n                  className=\"bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors\"\n                >\n                  Next: AI Scoring →\n                </button>\n              </div>\n            )}\n          </div>\n        );\n\n      case 'scoring':\n        return (\n          <div className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">AI Scoring & Analysis</h3>\n            \n            <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n              <h4 className=\"font-medium text-blue-800 mb-2\">🤖 AI Scoring Criteria</h4>\n              <ul className=\"text-blue-700 text-sm space-y-1\">\n                <li>• <strong>Strategic Alignment:</strong> How well the project aligns with business strategy</li>\n                <li>• <strong>ROI Potential:</strong> Expected return on investment based on historical data</li>\n                <li>• <strong>Risk Level:</strong> Implementation and business risks</li>\n                <li>• <strong>Implementation Ease:</strong> Complexity and resource requirements</li>\n              </ul>\n            </div>\n\n            {scoredProjects.length === 0 ? (\n              <div className=\"text-center py-8\">\n                <button\n                  onClick={runAIScoring}\n                  disabled={isProcessing}\n                  className=\"bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 disabled:bg-gray-400 transition-colors\"\n                >\n                  {isProcessing ? '🤖 AI Analyzing Projects...' : '🤖 Run AI Scoring'}\n                </button>\n              </div>\n            ) : (\n              <div className=\"space-y-4\">\n                <div className=\"bg-green-50 border border-green-200 rounded-lg p-4\">\n                  <h4 className=\"font-medium text-green-800\">✅ AI Scoring Complete</h4>\n                  <p className=\"text-green-700 text-sm\">\n                    All {scoredProjects.length} projects have been scored and analyzed.\n                  </p>\n                </div>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  {scoredProjects.map((project) => (\n                    <div key={project.id} className=\"border rounded-lg p-4\">\n                      <div className=\"flex justify-between items-start mb-3\">\n                        <h4 className=\"font-medium text-gray-900\">{project.name}</h4>\n                        <div className=\"text-right\">\n                          <div className=\"text-2xl font-bold text-indigo-600\">{project.ai_score}</div>\n                          <div className=\"text-xs text-gray-500\">AI Score</div>\n                        </div>\n                      </div>\n                      \n                      <div className=\"grid grid-cols-2 gap-2 text-sm mb-3\">\n                        <div>Strategic: {project.strategic_alignment}/100</div>\n                        <div>ROI: {project.roi_potential}/100</div>\n                        <div>Risk: {project.risk_level}/100</div>\n                        <div>Ease: {project.implementation_ease}/100</div>\n                      </div>\n                      \n                      <div className=\"text-xs text-gray-600\">\n                        <strong>Justification:</strong>\n                        <ul className=\"mt-1 space-y-1\">\n                          {project.justification.map((reason: string, index: number) => (\n                            <li key={index}>• {reason}</li>\n                          ))}\n                        </ul>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n\n                <button\n                  onClick={() => setCurrentStep('weights')}\n                  className=\"bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors\"\n                >\n                  Next: Set Weights →\n                </button>\n              </div>\n            )}\n          </div>\n        );\n\n      case 'weights':\n        return (\n          <div className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Set Strategic Weights</h3>\n            \n            <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n              <h4 className=\"font-medium text-yellow-800 mb-2\">⚖️ Weight Configuration</h4>\n              <p className=\"text-yellow-700 text-sm\">\n                Adjust the importance of each scoring criteria based on your strategic priorities.\n              </p>\n            </div>\n\n            <div className=\"space-y-4\">\n              {Object.entries(weights).map(([key, value]) => (\n                <div key={key} className=\"flex items-center space-x-4\">\n                  <div className=\"w-40\">\n                    <label className=\"text-sm font-medium text-gray-700 capitalize\">\n                      {key.replace('_', ' ')}\n                    </label>\n                  </div>\n                  <div className=\"flex-1\">\n                    <input\n                      type=\"range\"\n                      min=\"0\"\n                      max=\"1\"\n                      step=\"0.1\"\n                      value={value}\n                      onChange={(e) => setWeights({...weights, [key]: Number(e.target.value)})}\n                      className=\"w-full\"\n                    />\n                  </div>\n                  <div className=\"w-16 text-right\">\n                    <span className=\"text-sm font-medium\">{(value * 100).toFixed(0)}%</span>\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            <div className=\"bg-gray-50 rounded-lg p-4\">\n              <h4 className=\"font-medium text-gray-900 mb-2\">Optimization Goal</h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                {[\n                  { id: 'maximize_roi', label: 'Maximize ROI', icon: '💰', desc: 'Focus on highest return projects' },\n                  { id: 'minimize_risk', label: 'Minimize Risk', icon: '🛡️', desc: 'Prioritize low-risk projects' },\n                  { id: 'balanced', label: 'Balanced', icon: '⚖️', desc: 'Balance risk and return' },\n                ].map((goal) => (\n                  <div\n                    key={goal.id}\n                    className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${\n                      optimizationGoal === goal.id\n                        ? 'border-indigo-500 bg-indigo-50'\n                        : 'border-gray-200 hover:border-gray-300'\n                    }`}\n                    onClick={() => setOptimizationGoal(goal.id as any)}\n                  >\n                    <div className=\"text-center\">\n                      <div className=\"text-2xl mb-2\">{goal.icon}</div>\n                      <h5 className=\"font-medium text-gray-900\">{goal.label}</h5>\n                      <p className=\"text-sm text-gray-600 mt-1\">{goal.desc}</p>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            <div className=\"flex gap-4\">\n              <button\n                onClick={() => setCurrentStep('scoring')}\n                className=\"bg-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-400 transition-colors\"\n              >\n                ← Back\n              </button>\n              <button\n                onClick={() => setCurrentStep('optimize')}\n                className=\"bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors\"\n              >\n                Next: Optimize →\n              </button>\n            </div>\n          </div>\n        );\n\n      case 'optimize':\n        return (\n          <div className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Optimize CAPEX Allocation</h3>\n            \n            {!optimizedAllocation ? (\n              <div className=\"text-center py-8\">\n                <button\n                  onClick={runOptimization}\n                  disabled={isProcessing}\n                  className=\"bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 disabled:bg-gray-400 transition-colors\"\n                >\n                  {isProcessing ? '🎯 Optimizing Allocation...' : '🎯 Run Optimization'}\n                </button>\n              </div>\n            ) : (\n              <div className=\"space-y-6\">\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                  <div className=\"bg-white border rounded-lg p-6\">\n                    <h4 className=\"font-medium text-gray-900 mb-2\">💰 Total Budget</h4>\n                    <div className=\"text-2xl font-bold text-green-600\">\n                      ${optimizedAllocation.total_budget.toLocaleString()}\n                    </div>\n                  </div>\n                  <div className=\"bg-white border rounded-lg p-6\">\n                    <h4 className=\"font-medium text-gray-900 mb-2\">📈 Expected ROI</h4>\n                    <div className=\"text-2xl font-bold text-blue-600\">\n                      ${optimizedAllocation.expected_roi.toLocaleString()}\n                    </div>\n                  </div>\n                  <div className=\"bg-white border rounded-lg p-6\">\n                    <h4 className=\"font-medium text-gray-900 mb-2\">⚠️ Risk Score</h4>\n                    <div className=\"text-2xl font-bold text-orange-600\">\n                      {optimizedAllocation.risk_score.toFixed(1)}/100\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-white border rounded-lg p-6\">\n                  <h4 className=\"font-medium text-gray-900 mb-4\">🏆 Prioritized Project List</h4>\n                  <div className=\"space-y-3\">\n                    {optimizedAllocation.projects.map((project: any, index: number) => (\n                      <div key={project.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded\">\n                        <div className=\"flex items-center\">\n                          <div className=\"w-8 h-8 bg-indigo-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3\">\n                            {index + 1}\n                          </div>\n                          <div>\n                            <h5 className=\"font-medium text-gray-900\">{project.name}</h5>\n                            <p className=\"text-sm text-gray-600\">\n                              Score: {project.weighted_score.toFixed(1)} | Budget: ${project.budget.toLocaleString()}\n                            </p>\n                          </div>\n                        </div>\n                        <div className=\"text-right\">\n                          <div className=\"text-sm font-medium text-indigo-600\">\n                            Priority {index + 1}\n                          </div>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                <button\n                  onClick={() => setCurrentStep('whatif')}\n                  className=\"bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors\"\n                >\n                  Next: What-If Analysis →\n                </button>\n              </div>\n            )}\n          </div>\n        );\n\n      case 'whatif':\n        return (\n          <div className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">What-If Analysis</h3>\n            \n            <div className=\"bg-purple-50 border border-purple-200 rounded-lg p-4\">\n              <h4 className=\"font-medium text-purple-800 mb-2\">🔍 Interactive Analysis</h4>\n              <p className=\"text-purple-700 text-sm\">\n                Adjust project allocations and see real-time impact on ROI and risk.\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n              <div className=\"space-y-4\">\n                <h4 className=\"font-medium text-gray-900\">Adjust Allocations</h4>\n                {optimizedAllocation?.projects.slice(0, 3).map((project: any) => (\n                  <div key={project.id} className=\"border rounded-lg p-4\">\n                    <div className=\"flex justify-between items-center mb-2\">\n                      <h5 className=\"font-medium text-gray-900\">{project.name}</h5>\n                      <span className=\"text-sm text-gray-600\">${project.budget.toLocaleString()}</span>\n                    </div>\n                    <input\n                      type=\"range\"\n                      min=\"0\"\n                      max={project.budget * 2}\n                      value={project.budget}\n                      className=\"w-full\"\n                      onChange={() => {\n                        // Real-time recalculation would go here\n                      }}\n                    />\n                    <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\n                      <span>$0</span>\n                      <span>${(project.budget * 2).toLocaleString()}</span>\n                    </div>\n                  </div>\n                ))}\n              </div>\n\n              <div className=\"space-y-4\">\n                <h4 className=\"font-medium text-gray-900\">Impact Analysis</h4>\n                <div className=\"border rounded-lg p-4\">\n                  <h5 className=\"font-medium text-gray-900 mb-3\">Real-time Metrics</h5>\n                  <div className=\"space-y-2 text-sm\">\n                    <div className=\"flex justify-between\">\n                      <span>Total Budget:</span>\n                      <span className=\"font-medium\">${optimizedAllocation?.total_budget.toLocaleString()}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span>Expected ROI:</span>\n                      <span className=\"font-medium text-green-600\">+15.2%</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span>Risk Level:</span>\n                      <span className=\"font-medium text-orange-600\">Medium</span>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"border rounded-lg p-4\">\n                  <h5 className=\"font-medium text-gray-900 mb-3\">Scenario Comparison</h5>\n                  <div className=\"h-32 bg-gray-100 rounded flex items-center justify-center\">\n                    <span className=\"text-gray-500\">Impact Chart</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        );\n\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">🏗️ CAPEX Optimization</h2>\n        <p className=\"text-gray-600\">\n          AI-powered CAPEX prioritization with scoring, optimization, and what-if analysis.\n        </p>\n      </div>\n\n      {/* Progress Steps */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <div className=\"flex items-center justify-between mb-8\">\n          {steps.map((step, index) => (\n            <div key={step.id} className=\"flex items-center\">\n              <div\n                className={`\n                  flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors cursor-pointer\n                  ${step.completed || currentStep === step.id\n                    ? 'bg-indigo-600 border-indigo-600 text-white'\n                    : 'bg-white border-gray-300 text-gray-400'\n                  }\n                `}\n                onClick={() => setCurrentStep(step.id as any)}\n              >\n                {step.completed ? '✓' : step.icon}\n              </div>\n              <div className=\"ml-3 hidden md:block\">\n                <div className={`text-sm font-medium ${\n                  step.completed || currentStep === step.id ? 'text-indigo-600' : 'text-gray-400'\n                }`}>\n                  {step.label}\n                </div>\n              </div>\n              {index < steps.length - 1 && (\n                <div className={`w-12 h-0.5 mx-4 ${\n                  step.completed ? 'bg-indigo-600' : 'bg-gray-300'\n                }`} />\n              )}\n            </div>\n          ))}\n        </div>\n\n        {/* Step Content */}\n        {renderStepContent()}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AASO,SAAS,kBAAkB,KAAgD;QAAhD,EAAE,oBAAoB,EAA0B,GAAhD;;IAChC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4D;IACzG,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IACpD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC9D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,qBAAqB;QACrB,eAAe;QACf,YAAY;QACZ,qBAAqB;IACvB;IACA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiD;IACxG,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACpE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,QAAQ;QACZ;YAAE,IAAI;YAAU,OAAO;YAAqB,MAAM;YAAM,WAAW,UAAU,MAAM,GAAG;QAAE;QACxF;YAAE,IAAI;YAAW,OAAO;YAAc,MAAM;YAAM,WAAW,eAAe,MAAM,GAAG;QAAE;QACvF;YAAE,IAAI;YAAW,OAAO;YAAe,MAAM;YAAM,WAAW;QAAM;QACpE;YAAE,IAAI;YAAY,OAAO;YAAuB,MAAM;YAAM,WAAW,wBAAwB;QAAK;QACpG;YAAE,IAAI;YAAU,OAAO;YAAoB,MAAM;YAAM,WAAW;QAAM;KACzE;IAED,MAAM,eAAe;QACnB,IAAI;YACF,gBAAgB;YAEhB,+BAA+B;YAC/B,MAAM,SAAS,UAAU,GAAG,CAAC,CAAC,SAAS,QAAU,CAAC;oBAChD,GAAG,OAAO;oBACV,UAAU,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;oBAC3C,qBAAqB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;oBACtD,eAAe,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;oBAChD,YAAY,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;oBAC7C,qBAAqB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;oBACtD,eAAe;wBACb;wBACA;wBACA;qBACD;gBACH,CAAC;YAED,kBAAkB;YAClB,eAAe;QAEjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;QACrC,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI;YACF,gBAAgB;YAEhB,4BAA4B;YAC5B,MAAM,mBAAmB,eAAe,GAAG,CAAC,CAAA,UAAW,CAAC;oBACtD,GAAG,OAAO;oBACV,gBACE,QAAQ,mBAAmB,GAAG,QAAQ,mBAAmB,GACzD,QAAQ,aAAa,GAAG,QAAQ,aAAa,GAC7C,CAAC,MAAM,QAAQ,UAAU,IAAI,QAAQ,UAAU,GAAG,cAAc;oBAChE,QAAQ,mBAAmB,GAAG,QAAQ,mBAAmB;gBAE7D,CAAC;YAED,yBAAyB;YACzB,MAAM,YAAY,iBAAiB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,cAAc,GAAG,EAAE,cAAc;YAErF,uBAAuB;gBACrB,UAAU;gBACV,cAAc,UAAU,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,MAAM,IAAI,CAAC,GAAG;gBAClE,cAAc,UAAU,MAAM,CAAC,CAAC,KAAK,IAAM,MAAO,EAAE,aAAa,GAAG,CAAC,EAAE,MAAM,IAAI,CAAC,IAAI,KAAM;gBAC5F,YAAY,UAAU,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,UAAU,EAAE,KAAK,UAAU,MAAM;YACpF;YAEA,eAAe;YACf,iCAAA,2CAAA,qBAAuB;QAEzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;QACvC,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;wBAEnD,UAAU,MAAM,KAAK,kBACpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,6LAAC;oCACC,SAAS;wCACP,gCAAgC;wCAChC,MAAM,aAAa;4CACjB;gDAAE,IAAI;gDAAG,MAAM;gDAAwB,QAAQ;gDAAU,UAAU;gDAAkB,UAAU;4CAAO;4CACtG;gDAAE,IAAI;gDAAG,MAAM;gDAA4B,QAAQ;gDAAS,UAAU;gDAAc,UAAU;4CAAS;4CACvG;gDAAE,IAAI;gDAAG,MAAM;gDAA8B,QAAQ;gDAAS,UAAU;gDAAY,UAAU;4CAAM;4CACpG;gDAAE,IAAI;gDAAG,MAAM;gDAA2B,QAAQ;gDAAS,UAAU;gDAAa,UAAU;4CAAO;4CACnG;gDAAE,IAAI;gDAAG,MAAM;gDAA2B,QAAQ;gDAAS,UAAU;gDAAY,UAAU;4CAAS;yCACrG;wCACD,aAAa;oCACf;oCACA,WAAU;8CACX;;;;;;;;;;;iDAKH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAE,WAAU;;gDACV,UAAU,MAAM;gDAAC;gDAAwC,UAAU,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE,GAAG,cAAc;;;;;;;;;;;;;8CAI5H,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAM,WAAU;;0DACf,6LAAC;gDAAM,WAAU;0DACf,cAAA,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAkE;;;;;;sEAChF,6LAAC;4DAAG,WAAU;sEAAkE;;;;;;sEAChF,6LAAC;4DAAG,WAAU;sEAAkE;;;;;;sEAChF,6LAAC;4DAAG,WAAU;sEAAkE;;;;;;;;;;;;;;;;;0DAGpF,6LAAC;gDAAM,WAAU;0DACd,UAAU,GAAG,CAAC,CAAC,wBACd,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EACX,QAAQ,IAAI;;;;;;0EAEf,6LAAC;gEAAG,WAAU;;oEAAoD;oEAC9D,QAAQ,MAAM,CAAC,cAAc;;;;;;;0EAEjC,6LAAC;gEAAG,WAAU;0EACX,QAAQ,QAAQ;;;;;;0EAEnB,6LAAC;gEAAG,WAAU;0EACZ,cAAA,6LAAC;oEAAK,WAAW,AAAC,kCAIjB,OAHC,QAAQ,QAAQ,KAAK,SAAS,4BAC9B,QAAQ,QAAQ,KAAK,WAAW,kCAChC;8EAEC,QAAQ,QAAQ;;;;;;;;;;;;uDAhBd,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;8CAyB3B,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CACX;;;;;;;;;;;;;;;;;;YAQX,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAEpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAiC;;;;;;8CAC/C,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;;gDAAG;8DAAE,6LAAC;8DAAO;;;;;;gDAA6B;;;;;;;sDAC3C,6LAAC;;gDAAG;8DAAE,6LAAC;8DAAO;;;;;;gDAAuB;;;;;;;sDACrC,6LAAC;;gDAAG;8DAAE,6LAAC;8DAAO;;;;;;gDAAoB;;;;;;;sDAClC,6LAAC;;gDAAG;8DAAE,6LAAC;8DAAO;;;;;;gDAA6B;;;;;;;;;;;;;;;;;;;wBAI9C,eAAe,MAAM,KAAK,kBACzB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,eAAe,gCAAgC;;;;;;;;;;iDAIpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAE,WAAU;;gDAAyB;gDAC/B,eAAe,MAAM;gDAAC;;;;;;;;;;;;;8CAI/B,6LAAC;oCAAI,WAAU;8CACZ,eAAe,GAAG,CAAC,CAAC,wBACnB,6LAAC;4CAAqB,WAAU;;8DAC9B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA6B,QAAQ,IAAI;;;;;;sEACvD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAsC,QAAQ,QAAQ;;;;;;8EACrE,6LAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAI3C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;gEAAI;gEAAY,QAAQ,mBAAmB;gEAAC;;;;;;;sEAC7C,6LAAC;;gEAAI;gEAAM,QAAQ,aAAa;gEAAC;;;;;;;sEACjC,6LAAC;;gEAAI;gEAAO,QAAQ,UAAU;gEAAC;;;;;;;sEAC/B,6LAAC;;gEAAI;gEAAO,QAAQ,mBAAmB;gEAAC;;;;;;;;;;;;;8DAG1C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAO;;;;;;sEACR,6LAAC;4DAAG,WAAU;sEACX,QAAQ,aAAa,CAAC,GAAG,CAAC,CAAC,QAAgB,sBAC1C,6LAAC;;wEAAe;wEAAG;;mEAAV;;;;;;;;;;;;;;;;;2CApBP,QAAQ,EAAE;;;;;;;;;;8CA4BxB,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CACX;;;;;;;;;;;;;;;;;;YAQX,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAEpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,6LAAC;oCAAE,WAAU;8CAA0B;;;;;;;;;;;;sCAKzC,6LAAC;4BAAI,WAAU;sCACZ,OAAO,OAAO,CAAC,SAAS,GAAG,CAAC;oCAAC,CAAC,KAAK,MAAM;qDACxC,6LAAC;oCAAc,WAAU;;sDACvB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAM,WAAU;0DACd,IAAI,OAAO,CAAC,KAAK;;;;;;;;;;;sDAGtB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,MAAK;gDACL,KAAI;gDACJ,KAAI;gDACJ,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,WAAW;wDAAC,GAAG,OAAO;wDAAE,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACtE,WAAU;;;;;;;;;;;sDAGd,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;;oDAAuB,CAAC,QAAQ,GAAG,EAAE,OAAO,CAAC;oDAAG;;;;;;;;;;;;;mCAlB1D;;;;;;;;;;;sCAwBd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAiC;;;;;;8CAC/C,6LAAC;oCAAI,WAAU;8CACZ;wCACC;4CAAE,IAAI;4CAAgB,OAAO;4CAAgB,MAAM;4CAAM,MAAM;wCAAmC;wCAClG;4CAAE,IAAI;4CAAiB,OAAO;4CAAiB,MAAM;4CAAO,MAAM;wCAA+B;wCACjG;4CAAE,IAAI;4CAAY,OAAO;4CAAY,MAAM;4CAAM,MAAM;wCAA0B;qCAClF,CAAC,GAAG,CAAC,CAAC,qBACL,6LAAC;4CAEC,WAAW,AAAC,4DAIX,OAHC,qBAAqB,KAAK,EAAE,GACxB,mCACA;4CAEN,SAAS,IAAM,oBAAoB,KAAK,EAAE;sDAE1C,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAiB,KAAK,IAAI;;;;;;kEACzC,6LAAC;wDAAG,WAAU;kEAA6B,KAAK,KAAK;;;;;;kEACrD,6LAAC;wDAAE,WAAU;kEAA8B,KAAK,IAAI;;;;;;;;;;;;2CAXjD,KAAK,EAAE;;;;;;;;;;;;;;;;sCAkBpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CACX;;;;;;;;;;;;;;;;;;YAOT,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;wBAEnD,CAAC,oCACA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,eAAe,gCAAgC;;;;;;;;;;iDAIpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAiC;;;;;;8DAC/C,6LAAC;oDAAI,WAAU;;wDAAoC;wDAC/C,oBAAoB,YAAY,CAAC,cAAc;;;;;;;;;;;;;sDAGrD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAiC;;;;;;8DAC/C,6LAAC;oDAAI,WAAU;;wDAAmC;wDAC9C,oBAAoB,YAAY,CAAC,cAAc;;;;;;;;;;;;;sDAGrD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAiC;;;;;;8DAC/C,6LAAC;oDAAI,WAAU;;wDACZ,oBAAoB,UAAU,CAAC,OAAO,CAAC;wDAAG;;;;;;;;;;;;;;;;;;;8CAKjD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,6LAAC;4CAAI,WAAU;sDACZ,oBAAoB,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAc,sBAC/C,6LAAC;oDAAqB,WAAU;;sEAC9B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACZ,QAAQ;;;;;;8EAEX,6LAAC;;sFACC,6LAAC;4EAAG,WAAU;sFAA6B,QAAQ,IAAI;;;;;;sFACvD,6LAAC;4EAAE,WAAU;;gFAAwB;gFAC3B,QAAQ,cAAc,CAAC,OAAO,CAAC;gFAAG;gFAAa,QAAQ,MAAM,CAAC,cAAc;;;;;;;;;;;;;;;;;;;sEAI1F,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;;oEAAsC;oEACzC,QAAQ;;;;;;;;;;;;;mDAdd,QAAQ,EAAE;;;;;;;;;;;;;;;;8CAsB1B,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CACX;;;;;;;;;;;;;;;;;;YAQX,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAEpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,6LAAC;oCAAE,WAAU;8CAA0B;;;;;;;;;;;;sCAKzC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA4B;;;;;;wCACzC,gCAAA,0CAAA,oBAAqB,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,wBAC9C,6LAAC;gDAAqB,WAAU;;kEAC9B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAA6B,QAAQ,IAAI;;;;;;0EACvD,6LAAC;gEAAK,WAAU;;oEAAwB;oEAAE,QAAQ,MAAM,CAAC,cAAc;;;;;;;;;;;;;kEAEzE,6LAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,KAAK,QAAQ,MAAM,GAAG;wDACtB,OAAO,QAAQ,MAAM;wDACrB,WAAU;wDACV,UAAU;wDACR,wCAAwC;wDAC1C;;;;;;kEAEF,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;;oEAAK;oEAAE,CAAC,QAAQ,MAAM,GAAG,CAAC,EAAE,cAAc;;;;;;;;;;;;;;+CAjBrC,QAAQ,EAAE;;;;;;;;;;;8CAuBxB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA4B;;;;;;sDAC1C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAiC;;;;;;8DAC/C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;8EAAK;;;;;;8EACN,6LAAC;oEAAK,WAAU;;wEAAc;wEAAE,gCAAA,0CAAA,oBAAqB,YAAY,CAAC,cAAc;;;;;;;;;;;;;sEAElF,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;8EAAK;;;;;;8EACN,6LAAC;oEAAK,WAAU;8EAA6B;;;;;;;;;;;;sEAE/C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;8EAAK;;;;;;8EACN,6LAAC;oEAAK,WAAU;8EAA8B;;;;;;;;;;;;;;;;;;;;;;;;sDAKpD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAiC;;;;;;8DAC/C,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQ9C;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAM/B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;gCAAkB,WAAU;;kDAC3B,6LAAC;wCACC,WAAW,AAAC,4IAKT,OAHC,KAAK,SAAS,IAAI,gBAAgB,KAAK,EAAE,GACvC,+CACA,0CACH;wCAEH,SAAS,IAAM,eAAe,KAAK,EAAE;kDAEpC,KAAK,SAAS,GAAG,MAAM,KAAK,IAAI;;;;;;kDAEnC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAW,AAAC,uBAEhB,OADC,KAAK,SAAS,IAAI,gBAAgB,KAAK,EAAE,GAAG,oBAAoB;sDAE/D,KAAK,KAAK;;;;;;;;;;;oCAGd,QAAQ,MAAM,MAAM,GAAG,mBACtB,6LAAC;wCAAI,WAAW,AAAC,mBAEhB,OADC,KAAK,SAAS,GAAG,kBAAkB;;;;;;;+BAtB/B,KAAK,EAAE;;;;;;;;;;oBA8BpB;;;;;;;;;;;;;AAIT;GAjhBgB;KAAA", "debugId": null}}, {"offset": {"line": 7597, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/src/components/revenue-forecasting.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { apiClient } from '@/lib/api';\n\ninterface RevenueForecastingProps {\n  onForecastUpdate?: (forecast: any) => void;\n}\n\nexport function RevenueForecasting({ onForecastUpdate }: RevenueForecastingProps) {\n  const [currentStep, setCurrentStep] = useState<'scenario' | 'forecast' | 'drilldown' | 'compare'>('scenario');\n  const [selectedScenario, setSelectedScenario] = useState<string>('base');\n  const [forecastResults, setForecastResults] = useState<any>(null);\n  const [drilldownData, setDrilldownData] = useState<any>(null);\n  const [comparisonData, setComparisonData] = useState<any[]>([]);\n  const [isForecasting, setIsForecasting] = useState(false);\n  const [forecastMethod, setForecastMethod] = useState<'econometric' | 'ai' | 'hybrid'>('hybrid');\n\n  const scenarios = [\n    { id: 'base', name: 'Base Case', description: 'Current market conditions', growth: 0.05 },\n    { id: 'optimistic', name: 'Optimistic', description: 'Favorable market conditions', growth: 0.12 },\n    { id: 'pessimistic', name: 'Pessimistic', description: 'Challenging market conditions', growth: -0.02 },\n    { id: 'expansion', name: 'Market Expansion', description: 'New market entry', growth: 0.18 },\n  ];\n\n  const businessUnits = [\n    'Virgin Mobile Chile', 'Virgin Mobile Colombia', 'Virgin Mobile Mexico',\n    'Virgin Mobile Kuwait', 'Virgin Mobile KSA', 'FriendyMobile Oman',\n    'FriendyMobile KSA', 'FriendiPay KSA', 'FriendiPay Oman'\n  ];\n\n  const steps = [\n    { id: 'scenario', label: 'Select Scenario', icon: '🎯', completed: selectedScenario !== '' },\n    { id: 'forecast', label: 'AI Forecast', icon: '🤖', completed: forecastResults !== null },\n    { id: 'drilldown', label: 'Drill Down Analysis', icon: '🔍', completed: drilldownData !== null },\n    { id: 'compare', label: 'Compare Scenarios', icon: '📊', completed: comparisonData.length > 0 },\n  ];\n\n  const runRevenueForecast = async () => {\n    try {\n      setIsForecasting(true);\n      \n      // Use RevenueAgent for sophisticated forecasting\n      const response = await apiClient.runRevenueAgent({\n        business_units: businessUnits,\n        scenario: selectedScenario,\n        forecast_method: forecastMethod,\n        forecast_periods: 12,\n        include_churn_analysis: true,\n        include_arpu_analysis: true,\n        macro_integration: true,\n        scenarios: [selectedScenario],\n      });\n\n      if (response.success) {\n        setForecastResults(response.data);\n        setCurrentStep('drilldown');\n        onForecastUpdate?.(response.data);\n      }\n    } catch (error) {\n      console.error('Revenue forecast error:', error);\n    } finally {\n      setIsForecasting(false);\n    }\n  };\n\n  const runDrilldownAnalysis = async () => {\n    try {\n      setIsForecasting(true);\n      \n      // Use multiple backend services for comprehensive analysis\n      const [forecastService, elasticityAnalysis, timeSeriesAnalysis] = await Promise.all([\n        apiClient.createForecast({\n          data: forecastResults?.historical_data || [],\n          periods: 12,\n          method: forecastMethod,\n          context: 'revenue',\n          business_units: businessUnits,\n        }),\n        // Simulate elasticity analysis call\n        fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/analysis/elasticity`, {\n          method: 'POST',\n          headers: { 'Content-Type': 'application/json' },\n          body: JSON.stringify({\n            scenario: selectedScenario,\n            business_units: businessUnits,\n          }),\n        }),\n        // Time series analysis for trend decomposition\n        apiClient.analyzeTimeSeries({\n          data: forecastResults?.historical_data || [],\n          context: 'revenue_drivers',\n        }),\n      ]);\n\n      const drilldown = {\n        forecast_service: forecastService,\n        elasticity_analysis: elasticityAnalysis.ok ? await elasticityAnalysis.json() : null,\n        time_series_analysis: timeSeriesAnalysis,\n        revenue_drivers: {\n          customer_growth: 0.08,\n          arpu_growth: 0.03,\n          churn_impact: -0.02,\n          market_expansion: 0.05,\n        },\n        country_breakdown: businessUnits.map(unit => ({\n          unit,\n          contribution: Math.random() * 0.3 + 0.1,\n          growth_rate: Math.random() * 0.2 - 0.05,\n          risk_level: ['Low', 'Medium', 'High'][Math.floor(Math.random() * 3)],\n        })),\n      };\n\n      setDrilldownData(drilldown);\n      setCurrentStep('compare');\n    } catch (error) {\n      console.error('Drilldown analysis error:', error);\n    } finally {\n      setIsForecasting(false);\n    }\n  };\n\n  const runScenarioComparison = async () => {\n    try {\n      setIsForecasting(true);\n      \n      // Run forecasts for all scenarios\n      const comparisons = await Promise.all(\n        scenarios.map(async (scenario) => {\n          const response = await apiClient.runRevenueAgent({\n            business_units: businessUnits.slice(0, 3), // Limit for demo\n            scenario: scenario.id,\n            forecast_method: forecastMethod,\n            forecast_periods: 12,\n          });\n          \n          return {\n            scenario: scenario.name,\n            scenario_id: scenario.id,\n            forecast: response.data,\n            total_revenue: Math.random() * 50000000 + 100000000,\n            growth_rate: scenario.growth,\n            confidence: Math.random() * 0.3 + 0.7,\n          };\n        })\n      );\n\n      setComparisonData(comparisons);\n    } catch (error) {\n      console.error('Scenario comparison error:', error);\n    } finally {\n      setIsForecasting(false);\n    }\n  };\n\n  const renderStepContent = () => {\n    switch (currentStep) {\n      case 'scenario':\n        return (\n          <div className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Select Revenue Scenario</h3>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              {scenarios.map((scenario) => (\n                <div\n                  key={scenario.id}\n                  className={`p-6 border-2 rounded-lg cursor-pointer transition-colors ${\n                    selectedScenario === scenario.id\n                      ? 'border-indigo-500 bg-indigo-50'\n                      : 'border-gray-200 hover:border-gray-300'\n                  }`}\n                  onClick={() => setSelectedScenario(scenario.id)}\n                >\n                  <h4 className=\"font-semibold text-gray-900 mb-2\">{scenario.name}</h4>\n                  <p className=\"text-sm text-gray-600 mb-3\">{scenario.description}</p>\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-sm font-medium\">Expected Growth:</span>\n                    <span className={`font-bold ${\n                      scenario.growth > 0 ? 'text-green-600' : 'text-red-600'\n                    }`}>\n                      {(scenario.growth * 100).toFixed(1)}%\n                    </span>\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n              <h4 className=\"font-medium text-blue-800 mb-2\">🤖 AI Forecasting Method</h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                {[\n                  { id: 'econometric', name: 'Econometric', desc: 'ARIMA, time series analysis' },\n                  { id: 'ai', name: 'AI-Powered', desc: 'Bayesian, machine learning' },\n                  { id: 'hybrid', name: 'Hybrid', desc: 'Combined approach' },\n                ].map((method) => (\n                  <label key={method.id} className=\"flex items-center p-3 border rounded cursor-pointer\">\n                    <input\n                      type=\"radio\"\n                      name=\"forecast_method\"\n                      value={method.id}\n                      checked={forecastMethod === method.id}\n                      onChange={(e) => setForecastMethod(e.target.value as any)}\n                      className=\"mr-3\"\n                    />\n                    <div>\n                      <div className=\"font-medium text-blue-800\">{method.name}</div>\n                      <div className=\"text-xs text-blue-600\">{method.desc}</div>\n                    </div>\n                  </label>\n                ))}\n              </div>\n            </div>\n\n            <button\n              onClick={() => setCurrentStep('forecast')}\n              disabled={!selectedScenario}\n              className=\"bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 disabled:bg-gray-300 transition-colors\"\n            >\n              Next: Generate Forecast →\n            </button>\n          </div>\n        );\n\n      case 'forecast':\n        return (\n          <div className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">AI Revenue Forecast</h3>\n            \n            <div className=\"bg-green-50 border border-green-200 rounded-lg p-4\">\n              <h4 className=\"font-medium text-green-800 mb-2\">🎯 Selected Configuration</h4>\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-green-700\">\n                <div><strong>Scenario:</strong> {scenarios.find(s => s.id === selectedScenario)?.name}</div>\n                <div><strong>Method:</strong> {forecastMethod}</div>\n                <div><strong>Business Units:</strong> {businessUnits.length}</div>\n                <div><strong>Periods:</strong> 12 months</div>\n              </div>\n            </div>\n\n            {!forecastResults ? (\n              <div className=\"text-center py-8\">\n                <button\n                  onClick={runRevenueForecast}\n                  disabled={isForecasting}\n                  className=\"bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 disabled:bg-gray-400 transition-colors\"\n                >\n                  {isForecasting ? '🤖 AI Forecasting...' : '🤖 Generate AI Forecast'}\n                </button>\n              </div>\n            ) : (\n              <div className=\"space-y-6\">\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                  <div className=\"bg-white border rounded-lg p-6\">\n                    <h4 className=\"font-medium text-gray-900 mb-2\">📈 Total Revenue</h4>\n                    <div className=\"text-2xl font-bold text-green-600\">\n                      ${(Math.random() * 50000000 + 150000000).toLocaleString()}\n                    </div>\n                    <div className=\"text-sm text-gray-600\">12-month forecast</div>\n                  </div>\n                  <div className=\"bg-white border rounded-lg p-6\">\n                    <h4 className=\"font-medium text-gray-900 mb-2\">📊 Growth Rate</h4>\n                    <div className=\"text-2xl font-bold text-blue-600\">\n                      {(scenarios.find(s => s.id === selectedScenario)?.growth! * 100).toFixed(1)}%\n                    </div>\n                    <div className=\"text-sm text-gray-600\">Year-over-year</div>\n                  </div>\n                  <div className=\"bg-white border rounded-lg p-6\">\n                    <h4 className=\"font-medium text-gray-900 mb-2\">🎯 Confidence</h4>\n                    <div className=\"text-2xl font-bold text-purple-600\">\n                      {(Math.random() * 0.2 + 0.8).toFixed(1)}%\n                    </div>\n                    <div className=\"text-sm text-gray-600\">Model confidence</div>\n                  </div>\n                </div>\n\n                <div className=\"bg-white border rounded-lg p-6\">\n                  <h4 className=\"font-medium text-gray-900 mb-4\">📊 Forecast Visualization</h4>\n                  <div className=\"h-64 bg-gray-100 rounded flex items-center justify-center\">\n                    <span className=\"text-gray-500\">Revenue Forecast Chart</span>\n                  </div>\n                </div>\n\n                <button\n                  onClick={() => setCurrentStep('drilldown')}\n                  className=\"bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors\"\n                >\n                  Next: Drill Down Analysis →\n                </button>\n              </div>\n            )}\n          </div>\n        );\n\n      case 'drilldown':\n        return (\n          <div className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Drill Down Analysis</h3>\n            \n            {!drilldownData ? (\n              <div className=\"text-center py-8\">\n                <button\n                  onClick={runDrilldownAnalysis}\n                  disabled={isForecasting}\n                  className=\"bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 disabled:bg-gray-400 transition-colors\"\n                >\n                  {isForecasting ? '🔍 Analyzing Drivers...' : '🔍 Analyze Revenue Drivers'}\n                </button>\n              </div>\n            ) : (\n              <div className=\"space-y-6\">\n                {/* Revenue Drivers */}\n                <div className=\"bg-white border rounded-lg p-6\">\n                  <h4 className=\"font-medium text-gray-900 mb-4\">🎯 Revenue Growth Drivers</h4>\n                  <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                    {Object.entries(drilldownData.revenue_drivers).map(([driver, impact]) => (\n                      <div key={driver} className=\"text-center p-3 bg-gray-50 rounded\">\n                        <div className={`text-lg font-bold ${\n                          (impact as number) > 0 ? 'text-green-600' : 'text-red-600'\n                        }`}>\n                          {((impact as number) * 100).toFixed(1)}%\n                        </div>\n                        <div className=\"text-sm text-gray-600 capitalize\">\n                          {driver.replace('_', ' ')}\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Country Breakdown */}\n                <div className=\"bg-white border rounded-lg p-6\">\n                  <h4 className=\"font-medium text-gray-900 mb-4\">🌍 Country/Business Unit Analysis</h4>\n                  <div className=\"space-y-3\">\n                    {drilldownData.country_breakdown.map((unit: any, index: number) => (\n                      <div key={index} className=\"flex items-center justify-between p-3 bg-gray-50 rounded\">\n                        <div className=\"flex-1\">\n                          <h5 className=\"font-medium text-gray-900\">{unit.unit}</h5>\n                          <div className=\"text-sm text-gray-600\">\n                            Contribution: {(unit.contribution * 100).toFixed(1)}% | \n                            Growth: {(unit.growth_rate * 100).toFixed(1)}%\n                          </div>\n                        </div>\n                        <div className=\"text-right\">\n                          <span className={`px-2 py-1 text-xs rounded-full ${\n                            unit.risk_level === 'Low' ? 'bg-green-100 text-green-800' :\n                            unit.risk_level === 'Medium' ? 'bg-yellow-100 text-yellow-800' :\n                            'bg-red-100 text-red-800'\n                          }`}>\n                            {unit.risk_level} Risk\n                          </span>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                <button\n                  onClick={() => setCurrentStep('compare')}\n                  className=\"bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors\"\n                >\n                  Next: Compare Scenarios →\n                </button>\n              </div>\n            )}\n          </div>\n        );\n\n      case 'compare':\n        return (\n          <div className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Scenario Comparison</h3>\n            \n            {comparisonData.length === 0 ? (\n              <div className=\"text-center py-8\">\n                <button\n                  onClick={runScenarioComparison}\n                  disabled={isForecasting}\n                  className=\"bg-orange-600 text-white px-6 py-3 rounded-lg hover:bg-orange-700 disabled:bg-gray-400 transition-colors\"\n                >\n                  {isForecasting ? '📊 Comparing Scenarios...' : '📊 Compare All Scenarios'}\n                </button>\n              </div>\n            ) : (\n              <div className=\"space-y-6\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n                  {comparisonData.map((comparison, index) => (\n                    <div key={index} className=\"bg-white border rounded-lg p-6\">\n                      <h4 className=\"font-medium text-gray-900 mb-3\">{comparison.scenario}</h4>\n                      <div className=\"space-y-2\">\n                        <div>\n                          <span className=\"text-sm text-gray-600\">Total Revenue:</span>\n                          <div className=\"font-bold text-green-600\">\n                            ${comparison.total_revenue.toLocaleString()}\n                          </div>\n                        </div>\n                        <div>\n                          <span className=\"text-sm text-gray-600\">Growth Rate:</span>\n                          <div className={`font-bold ${\n                            comparison.growth_rate > 0 ? 'text-green-600' : 'text-red-600'\n                          }`}>\n                            {(comparison.growth_rate * 100).toFixed(1)}%\n                          </div>\n                        </div>\n                        <div>\n                          <span className=\"text-sm text-gray-600\">Confidence:</span>\n                          <div className=\"font-bold text-blue-600\">\n                            {(comparison.confidence * 100).toFixed(1)}%\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n\n                <div className=\"bg-white border rounded-lg p-6\">\n                  <h4 className=\"font-medium text-gray-900 mb-4\">📊 Side-by-Side Comparison</h4>\n                  <div className=\"h-64 bg-gray-100 rounded flex items-center justify-center\">\n                    <span className=\"text-gray-500\">Scenario Comparison Chart</span>\n                  </div>\n                </div>\n\n                <div className=\"bg-green-50 border border-green-200 rounded-lg p-4\">\n                  <h4 className=\"font-medium text-green-800 mb-2\">✅ Analysis Complete</h4>\n                  <p className=\"text-green-700 text-sm\">\n                    Revenue forecasting analysis is complete. You can now export forecasts or create reports.\n                  </p>\n                </div>\n              </div>\n            )}\n          </div>\n        );\n\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">📈 Revenue Forecasting</h2>\n        <p className=\"text-gray-600\">\n          AI-powered revenue prediction with scenario analysis, driver attribution, and country-level drill-down.\n        </p>\n      </div>\n\n      {/* Progress Steps */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <div className=\"flex items-center justify-between mb-8\">\n          {steps.map((step, index) => (\n            <div key={step.id} className=\"flex items-center\">\n              <div\n                className={`\n                  flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors cursor-pointer\n                  ${step.completed || currentStep === step.id\n                    ? 'bg-indigo-600 border-indigo-600 text-white'\n                    : 'bg-white border-gray-300 text-gray-400'\n                  }\n                `}\n                onClick={() => setCurrentStep(step.id as any)}\n              >\n                {step.completed ? '✓' : step.icon}\n              </div>\n              <div className=\"ml-3 hidden md:block\">\n                <div className={`text-sm font-medium ${\n                  step.completed || currentStep === step.id ? 'text-indigo-600' : 'text-gray-400'\n                }`}>\n                  {step.label}\n                </div>\n              </div>\n              {index < steps.length - 1 && (\n                <div className={`w-12 h-0.5 mx-4 ${\n                  step.completed ? 'bg-indigo-600' : 'bg-gray-300'\n                }`} />\n              )}\n            </div>\n          ))}\n        </div>\n\n        {/* Step Content */}\n        {renderStepContent()}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;AAgFiB;;AA9EjB;AACA;;;AAHA;;;AASO,SAAS,mBAAmB,KAA6C;QAA7C,EAAE,gBAAgB,EAA2B,GAA7C;;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqD;IAClG,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAC5D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACxD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC9D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmC;IAEtF,MAAM,YAAY;QAChB;YAAE,IAAI;YAAQ,MAAM;YAAa,aAAa;YAA6B,QAAQ;QAAK;QACxF;YAAE,IAAI;YAAc,MAAM;YAAc,aAAa;YAA+B,QAAQ;QAAK;QACjG;YAAE,IAAI;YAAe,MAAM;YAAe,aAAa;YAAiC,QAAQ,CAAC;QAAK;QACtG;YAAE,IAAI;YAAa,MAAM;YAAoB,aAAa;YAAoB,QAAQ;QAAK;KAC5F;IAED,MAAM,gBAAgB;QACpB;QAAuB;QAA0B;QACjD;QAAwB;QAAqB;QAC7C;QAAqB;QAAkB;KACxC;IAED,MAAM,QAAQ;QACZ;YAAE,IAAI;YAAY,OAAO;YAAmB,MAAM;YAAM,WAAW,qBAAqB;QAAG;QAC3F;YAAE,IAAI;YAAY,OAAO;YAAe,MAAM;YAAM,WAAW,oBAAoB;QAAK;QACxF;YAAE,IAAI;YAAa,OAAO;YAAuB,MAAM;YAAM,WAAW,kBAAkB;QAAK;QAC/F;YAAE,IAAI;YAAW,OAAO;YAAqB,MAAM;YAAM,WAAW,eAAe,MAAM,GAAG;QAAE;KAC/F;IAED,MAAM,qBAAqB;QACzB,IAAI;YACF,iBAAiB;YAEjB,iDAAiD;YACjD,MAAM,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,eAAe,CAAC;gBAC/C,gBAAgB;gBAChB,UAAU;gBACV,iBAAiB;gBACjB,kBAAkB;gBAClB,wBAAwB;gBACxB,uBAAuB;gBACvB,mBAAmB;gBACnB,WAAW;oBAAC;iBAAiB;YAC/B;YAEA,IAAI,SAAS,OAAO,EAAE;gBACpB,mBAAmB,SAAS,IAAI;gBAChC,eAAe;gBACf,6BAAA,uCAAA,iBAAmB,SAAS,IAAI;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,iBAAiB;YAEjB,2DAA2D;YAC3D,MAAM,CAAC,iBAAiB,oBAAoB,mBAAmB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAClF,oHAAA,CAAA,YAAS,CAAC,cAAc,CAAC;oBACvB,MAAM,CAAA,4BAAA,sCAAA,gBAAiB,eAAe,KAAI,EAAE;oBAC5C,SAAS;oBACT,QAAQ;oBACR,SAAS;oBACT,gBAAgB;gBAClB;gBACA,oCAAoC;gBACpC,MAAM,AAAC,GAAkC,kEAAA,6BAA2B;oBAClE,QAAQ;oBACR,SAAS;wBAAE,gBAAgB;oBAAmB;oBAC9C,MAAM,KAAK,SAAS,CAAC;wBACnB,UAAU;wBACV,gBAAgB;oBAClB;gBACF;gBACA,+CAA+C;gBAC/C,oHAAA,CAAA,YAAS,CAAC,iBAAiB,CAAC;oBAC1B,MAAM,CAAA,4BAAA,sCAAA,gBAAiB,eAAe,KAAI,EAAE;oBAC5C,SAAS;gBACX;aACD;YAED,MAAM,YAAY;gBAChB,kBAAkB;gBAClB,qBAAqB,mBAAmB,EAAE,GAAG,MAAM,mBAAmB,IAAI,KAAK;gBAC/E,sBAAsB;gBACtB,iBAAiB;oBACf,iBAAiB;oBACjB,aAAa;oBACb,cAAc,CAAC;oBACf,kBAAkB;gBACpB;gBACA,mBAAmB,cAAc,GAAG,CAAC,CAAA,OAAQ,CAAC;wBAC5C;wBACA,cAAc,KAAK,MAAM,KAAK,MAAM;wBACpC,aAAa,KAAK,MAAM,KAAK,MAAM;wBACnC,YAAY;4BAAC;4BAAO;4BAAU;yBAAO,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,GAAG;oBACtE,CAAC;YACH;YAEA,iBAAiB;YACjB,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI;YACF,iBAAiB;YAEjB,kCAAkC;YAClC,MAAM,cAAc,MAAM,QAAQ,GAAG,CACnC,UAAU,GAAG,CAAC,OAAO;gBACnB,MAAM,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,eAAe,CAAC;oBAC/C,gBAAgB,cAAc,KAAK,CAAC,GAAG;oBACvC,UAAU,SAAS,EAAE;oBACrB,iBAAiB;oBACjB,kBAAkB;gBACpB;gBAEA,OAAO;oBACL,UAAU,SAAS,IAAI;oBACvB,aAAa,SAAS,EAAE;oBACxB,UAAU,SAAS,IAAI;oBACvB,eAAe,KAAK,MAAM,KAAK,WAAW;oBAC1C,aAAa,SAAS,MAAM;oBAC5B,YAAY,KAAK,MAAM,KAAK,MAAM;gBACpC;YACF;YAGF,kBAAkB;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAEpD,6LAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAC,yBACd,6LAAC;oCAEC,WAAW,AAAC,4DAIX,OAHC,qBAAqB,SAAS,EAAE,GAC5B,mCACA;oCAEN,SAAS,IAAM,oBAAoB,SAAS,EAAE;;sDAE9C,6LAAC;4CAAG,WAAU;sDAAoC,SAAS,IAAI;;;;;;sDAC/D,6LAAC;4CAAE,WAAU;sDAA8B,SAAS,WAAW;;;;;;sDAC/D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAsB;;;;;;8DACtC,6LAAC;oDAAK,WAAW,AAAC,aAEjB,OADC,SAAS,MAAM,GAAG,IAAI,mBAAmB;;wDAExC,CAAC,SAAS,MAAM,GAAG,GAAG,EAAE,OAAO,CAAC;wDAAG;;;;;;;;;;;;;;mCAfnC,SAAS,EAAE;;;;;;;;;;sCAsBtB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAiC;;;;;;8CAC/C,6LAAC;oCAAI,WAAU;8CACZ;wCACC;4CAAE,IAAI;4CAAe,MAAM;4CAAe,MAAM;wCAA8B;wCAC9E;4CAAE,IAAI;4CAAM,MAAM;4CAAc,MAAM;wCAA6B;wCACnE;4CAAE,IAAI;4CAAU,MAAM;4CAAU,MAAM;wCAAoB;qCAC3D,CAAC,GAAG,CAAC,CAAC,uBACL,6LAAC;4CAAsB,WAAU;;8DAC/B,6LAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO,OAAO,EAAE;oDAChB,SAAS,mBAAmB,OAAO,EAAE;oDACrC,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;oDACjD,WAAU;;;;;;8DAEZ,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAA6B,OAAO,IAAI;;;;;;sEACvD,6LAAC;4DAAI,WAAU;sEAAyB,OAAO,IAAI;;;;;;;;;;;;;2CAX3C,OAAO,EAAE;;;;;;;;;;;;;;;;sCAkB3B,6LAAC;4BACC,SAAS,IAAM,eAAe;4BAC9B,UAAU,CAAC;4BACX,WAAU;sCACX;;;;;;;;;;;;YAMP,KAAK;oBAQsC,iBA8BzB;gBArChB,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAEpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAkC;;;;;;8CAChD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DAAI,6LAAC;8DAAO;;;;;;gDAAkB;iDAAE,kBAAA,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,+BAA7B,sCAAA,gBAAgD,IAAI;;;;;;;sDACrF,6LAAC;;8DAAI,6LAAC;8DAAO;;;;;;gDAAgB;gDAAE;;;;;;;sDAC/B,6LAAC;;8DAAI,6LAAC;8DAAO;;;;;;gDAAwB;gDAAE,cAAc,MAAM;;;;;;;sDAC3D,6LAAC;;8DAAI,6LAAC;8DAAO;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;wBAIjC,CAAC,gCACA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,gBAAgB,yBAAyB;;;;;;;;;;iDAI9C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAiC;;;;;;8DAC/C,6LAAC;oDAAI,WAAU;;wDAAoC;wDAC/C,CAAC,KAAK,MAAM,KAAK,WAAW,SAAS,EAAE,cAAc;;;;;;;8DAEzD,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAiC;;;;;;8DAC/C,6LAAC;oDAAI,WAAU;;wDACZ,CAAC,EAAA,mBAAA,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,+BAA7B,uCAAA,iBAAgD,MAAM,IAAI,GAAG,EAAE,OAAO,CAAC;wDAAG;;;;;;;8DAE9E,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAiC;;;;;;8DAC/C,6LAAC;oDAAI,WAAU;;wDACZ,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,EAAE,OAAO,CAAC;wDAAG;;;;;;;8DAE1C,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAI3C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;;;;;;8CAIpC,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CACX;;;;;;;;;;;;;;;;;;YAQX,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;wBAEnD,CAAC,8BACA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,gBAAgB,4BAA4B;;;;;;;;;;iDAIjD,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,6LAAC;4CAAI,WAAU;sDACZ,OAAO,OAAO,CAAC,cAAc,eAAe,EAAE,GAAG,CAAC;oDAAC,CAAC,QAAQ,OAAO;qEAClE,6LAAC;oDAAiB,WAAU;;sEAC1B,6LAAC;4DAAI,WAAW,AAAC,qBAEhB,OADC,AAAC,SAAoB,IAAI,mBAAmB;;gEAE3C,CAAC,AAAC,SAAoB,GAAG,EAAE,OAAO,CAAC;gEAAG;;;;;;;sEAEzC,6LAAC;4DAAI,WAAU;sEACZ,OAAO,OAAO,CAAC,KAAK;;;;;;;mDAPf;;;;;;;;;;;;;;;;;8CAehB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,6LAAC;4CAAI,WAAU;sDACZ,cAAc,iBAAiB,CAAC,GAAG,CAAC,CAAC,MAAW,sBAC/C,6LAAC;oDAAgB,WAAU;;sEACzB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAA6B,KAAK,IAAI;;;;;;8EACpD,6LAAC;oEAAI,WAAU;;wEAAwB;wEACtB,CAAC,KAAK,YAAY,GAAG,GAAG,EAAE,OAAO,CAAC;wEAAG;wEAC3C,CAAC,KAAK,WAAW,GAAG,GAAG,EAAE,OAAO,CAAC;wEAAG;;;;;;;;;;;;;sEAGjD,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAW,AAAC,kCAIjB,OAHC,KAAK,UAAU,KAAK,QAAQ,gCAC5B,KAAK,UAAU,KAAK,WAAW,kCAC/B;;oEAEC,KAAK,UAAU;oEAAC;;;;;;;;;;;;;mDAdb;;;;;;;;;;;;;;;;8CAsBhB,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CACX;;;;;;;;;;;;;;;;;;YAQX,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;wBAEnD,eAAe,MAAM,KAAK,kBACzB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,gBAAgB,8BAA8B;;;;;;;;;;iDAInD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,eAAe,GAAG,CAAC,CAAC,YAAY,sBAC/B,6LAAC;4CAAgB,WAAU;;8DACzB,6LAAC;oDAAG,WAAU;8DAAkC,WAAW,QAAQ;;;;;;8DACnE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,6LAAC;oEAAI,WAAU;;wEAA2B;wEACtC,WAAW,aAAa,CAAC,cAAc;;;;;;;;;;;;;sEAG7C,6LAAC;;8EACC,6LAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,6LAAC;oEAAI,WAAW,AAAC,aAEhB,OADC,WAAW,WAAW,GAAG,IAAI,mBAAmB;;wEAE/C,CAAC,WAAW,WAAW,GAAG,GAAG,EAAE,OAAO,CAAC;wEAAG;;;;;;;;;;;;;sEAG/C,6LAAC;;8EACC,6LAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,6LAAC;oEAAI,WAAU;;wEACZ,CAAC,WAAW,UAAU,GAAG,GAAG,EAAE,OAAO,CAAC;wEAAG;;;;;;;;;;;;;;;;;;;;2CApBxC;;;;;;;;;;8CA4Bd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;;;;;;8CAIpC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAkC;;;;;;sDAChD,6LAAC;4CAAE,WAAU;sDAAyB;;;;;;;;;;;;;;;;;;;;;;;;YASlD;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAM/B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;gCAAkB,WAAU;;kDAC3B,6LAAC;wCACC,WAAW,AAAC,4IAKT,OAHC,KAAK,SAAS,IAAI,gBAAgB,KAAK,EAAE,GACvC,+CACA,0CACH;wCAEH,SAAS,IAAM,eAAe,KAAK,EAAE;kDAEpC,KAAK,SAAS,GAAG,MAAM,KAAK,IAAI;;;;;;kDAEnC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAW,AAAC,uBAEhB,OADC,KAAK,SAAS,IAAI,gBAAgB,KAAK,EAAE,GAAG,oBAAoB;sDAE/D,KAAK,KAAK;;;;;;;;;;;oCAGd,QAAQ,MAAM,MAAM,GAAG,mBACtB,6LAAC;wCAAI,WAAW,AAAC,mBAEhB,OADC,KAAK,SAAS,GAAG,kBAAkB;;;;;;;+BAtB/B,KAAK,EAAE;;;;;;;;;;oBA8BpB;;;;;;;;;;;;;AAIT;GA3dgB;KAAA", "debugId": null}}, {"offset": {"line": 8777, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/src/components/insights-reports.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { apiClient } from '@/lib/api';\n\ninterface InsightsReportsProps {\n  onReportUpdate?: (report: any) => void;\n}\n\nexport function InsightsReports({ onReportUpdate }: InsightsReportsProps) {\n  const [currentStep, setCurrentStep] = useState<'generate' | 'customize' | 'export'>('generate');\n  const [generatedInsights, setGeneratedInsights] = useState<any>(null);\n  const [selectedAudience, setSelectedAudience] = useState<'cfo' | 'country_head' | 'board' | 'technical'>('cfo');\n  const [selectedKPIs, setSelectedKPIs] = useState<string[]>(['revenue', 'growth', 'risk']);\n  const [reportFormat, setReportFormat] = useState<'pdf' | 'powerpoint' | 'dashboard'>('pdf');\n  const [isGenerating, setIsGenerating] = useState(false);\n\n  const audiences = [\n    { \n      id: 'cfo', \n      name: 'CFO / Finance Team', \n      icon: '💰',\n      focus: 'Financial metrics, ROI, cost optimization',\n      kpis: ['revenue', 'profit_margin', 'capex_roi', 'cost_efficiency']\n    },\n    { \n      id: 'country_head', \n      name: 'Country Head / GM', \n      icon: '🌍',\n      focus: 'Market performance, growth opportunities',\n      kpis: ['market_share', 'customer_growth', 'arpu', 'competitive_position']\n    },\n    { \n      id: 'board', \n      name: 'Board / Executive', \n      icon: '👔',\n      focus: 'Strategic overview, high-level trends',\n      kpis: ['total_revenue', 'growth_rate', 'market_expansion', 'strategic_initiatives']\n    },\n    { \n      id: 'technical', \n      name: 'Technical Team', \n      icon: '🔧',\n      focus: 'Implementation details, technical metrics',\n      kpis: ['system_performance', 'data_quality', 'model_accuracy', 'technical_debt']\n    },\n  ];\n\n  const availableKPIs = [\n    { id: 'revenue', name: 'Total Revenue', category: 'financial' },\n    { id: 'growth', name: 'Growth Rate', category: 'financial' },\n    { id: 'profit_margin', name: 'Profit Margin', category: 'financial' },\n    { id: 'capex_roi', name: 'CAPEX ROI', category: 'financial' },\n    { id: 'cost_efficiency', name: 'Cost Efficiency', category: 'operational' },\n    { id: 'market_share', name: 'Market Share', category: 'market' },\n    { id: 'customer_growth', name: 'Customer Growth', category: 'market' },\n    { id: 'arpu', name: 'ARPU', category: 'market' },\n    { id: 'churn_rate', name: 'Churn Rate', category: 'operational' },\n    { id: 'risk', name: 'Risk Assessment', category: 'risk' },\n  ];\n\n  const steps = [\n    { id: 'generate', label: 'Generate Insights', icon: '🤖', completed: generatedInsights !== null },\n    { id: 'customize', label: 'Customize Report', icon: '🎨', completed: false },\n    { id: 'export', label: 'Export & Share', icon: '📤', completed: false },\n  ];\n\n  const generateInsights = async () => {\n    try {\n      setIsGenerating(true);\n      \n      // Use multiple backend services for comprehensive insights\n      const [consolidationReport, financialAnalysis, naturalLanguageInsights] = await Promise.all([\n        // ConsolidationAgent for executive reporting\n        apiClient.runRevenueAgent({\n          business_units: ['Virgin Mobile Chile', 'Virgin Mobile Colombia', 'FriendyMobile Oman'],\n          analysis_type: 'consolidation',\n          audience: selectedAudience,\n        }),\n        // FinancialRatiosCalculator for KPI analysis\n        apiClient.analyzeFinancialRatios({\n          financial_data: {\n            revenue: 150000000,\n            operating_expenses: 120000000,\n            capex: 25000000,\n            customers: 2500000,\n            arpu: 60,\n          },\n          industry_type: 'telecom',\n        }),\n        // Simulate NaturalLanguageQueryEngine call\n        fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/insights/generate`, {\n          method: 'POST',\n          headers: { 'Content-Type': 'application/json' },\n          body: JSON.stringify({\n            audience: selectedAudience,\n            kpis: selectedKPIs,\n            data_sources: ['revenue', 'capex', 'macro'],\n          }),\n        }),\n      ]);\n\n      const insights = {\n        consolidation: consolidationReport.data,\n        financial_analysis: financialAnalysis.analysis,\n        natural_language: naturalLanguageInsights.ok ? await naturalLanguageInsights.json() : null,\n        executive_summary: generateExecutiveSummary(selectedAudience),\n        key_findings: generateKeyFindings(),\n        recommendations: generateRecommendations(selectedAudience),\n        kpi_dashboard: generateKPIDashboard(selectedKPIs),\n      };\n\n      setGeneratedInsights(insights);\n      setCurrentStep('customize');\n      onReportUpdate?.(insights);\n      \n    } catch (error) {\n      console.error('Insights generation error:', error);\n    } finally {\n      setIsGenerating(false);\n    }\n  };\n\n  const generateExecutiveSummary = (audience: string) => {\n    const summaries = {\n      cfo: \"Financial performance shows strong revenue growth of 8.5% YoY with improved cost efficiency. CAPEX ROI of 15.2% exceeds target. Recommend continued investment in high-performing markets.\",\n      country_head: \"Market expansion strategy delivering results with 12% customer growth in key segments. ARPU improvement of 5% driven by premium service adoption. Competitive positioning strengthened.\",\n      board: \"Strategic initiatives on track with total revenue reaching $150M. Market expansion successful in 3 new regions. Risk-adjusted returns exceed industry benchmarks by 3.2%.\",\n      technical: \"System performance metrics within acceptable ranges. Data quality scores improved to 94%. Model accuracy for revenue forecasting at 87%. Technical debt reduction program 60% complete.\",\n    };\n    return summaries[audience as keyof typeof summaries];\n  };\n\n  const generateKeyFindings = () => [\n    \"Revenue growth accelerating in Q4 with 12% increase over previous quarter\",\n    \"CAPEX optimization program delivered $5M in cost savings while maintaining service quality\",\n    \"Customer acquisition costs decreased by 18% through improved targeting\",\n    \"Market expansion in Colombia showing 25% higher ARPU than projected\",\n    \"Risk assessment indicates low probability of major market disruption\",\n  ];\n\n  const generateRecommendations = (audience: string) => {\n    const recommendations = {\n      cfo: [\n        \"Increase CAPEX allocation to high-ROI projects by 15%\",\n        \"Implement cost optimization program in underperforming segments\",\n        \"Consider debt refinancing to take advantage of favorable rates\",\n      ],\n      country_head: [\n        \"Accelerate premium service rollout in high-ARPU segments\",\n        \"Expand customer acquisition in rural markets\",\n        \"Strengthen competitive positioning through service differentiation\",\n      ],\n      board: [\n        \"Approve market expansion to 2 additional countries\",\n        \"Consider strategic acquisition opportunities\",\n        \"Increase dividend payout ratio to 35%\",\n      ],\n      technical: [\n        \"Upgrade analytics infrastructure to support real-time processing\",\n        \"Implement advanced ML models for churn prediction\",\n        \"Enhance data governance framework\",\n      ],\n    };\n    return recommendations[audience as keyof typeof recommendations];\n  };\n\n  const generateKPIDashboard = (kpis: string[]) => {\n    return kpis.map(kpi => ({\n      id: kpi,\n      name: availableKPIs.find(k => k.id === kpi)?.name || kpi,\n      value: Math.random() * 100,\n      trend: Math.random() > 0.5 ? 'up' : 'down',\n      change: (Math.random() * 20 - 10).toFixed(1),\n    }));\n  };\n\n  const renderStepContent = () => {\n    switch (currentStep) {\n      case 'generate':\n        return (\n          <div className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Generate AI Insights</h3>\n            \n            <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n              <h4 className=\"font-medium text-blue-800 mb-2\">🤖 Natural Language Insights</h4>\n              <p className=\"text-blue-700 text-sm\">\n                Our AI will analyze all your data and generate plain-English insights tailored to your audience.\n              </p>\n            </div>\n\n            <div>\n              <h4 className=\"font-medium text-gray-900 mb-3\">Select Target Audience</h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                {audiences.map((audience) => (\n                  <div\n                    key={audience.id}\n                    className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${\n                      selectedAudience === audience.id\n                        ? 'border-indigo-500 bg-indigo-50'\n                        : 'border-gray-200 hover:border-gray-300'\n                    }`}\n                    onClick={() => setSelectedAudience(audience.id as any)}\n                  >\n                    <div className=\"flex items-start\">\n                      <span className=\"text-2xl mr-3\">{audience.icon}</span>\n                      <div className=\"flex-1\">\n                        <h5 className=\"font-medium text-gray-900\">{audience.name}</h5>\n                        <p className=\"text-sm text-gray-600 mt-1\">{audience.focus}</p>\n                        <div className=\"mt-2 flex flex-wrap gap-1\">\n                          {audience.kpis.slice(0, 3).map((kpi) => (\n                            <span key={kpi} className=\"px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded\">\n                              {kpi.replace('_', ' ')}\n                            </span>\n                          ))}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            <div>\n              <h4 className=\"font-medium text-gray-900 mb-3\">Select Key KPIs</h4>\n              <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3\">\n                {availableKPIs.map((kpi) => (\n                  <label key={kpi.id} className=\"flex items-center p-2 border rounded cursor-pointer\">\n                    <input\n                      type=\"checkbox\"\n                      checked={selectedKPIs.includes(kpi.id)}\n                      onChange={(e) => {\n                        if (e.target.checked) {\n                          setSelectedKPIs([...selectedKPIs, kpi.id]);\n                        } else {\n                          setSelectedKPIs(selectedKPIs.filter(k => k !== kpi.id));\n                        }\n                      }}\n                      className=\"mr-2\"\n                    />\n                    <span className=\"text-sm\">{kpi.name}</span>\n                  </label>\n                ))}\n              </div>\n            </div>\n\n            {!generatedInsights ? (\n              <div className=\"text-center py-8\">\n                <button\n                  onClick={generateInsights}\n                  disabled={isGenerating || selectedKPIs.length === 0}\n                  className=\"bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 disabled:bg-gray-400 transition-colors\"\n                >\n                  {isGenerating ? '🤖 Generating Insights...' : '🤖 Generate AI Insights'}\n                </button>\n              </div>\n            ) : (\n              <div className=\"space-y-6\">\n                <div className=\"bg-green-50 border border-green-200 rounded-lg p-4\">\n                  <h4 className=\"font-medium text-green-800\">✅ Insights Generated</h4>\n                  <p className=\"text-green-700 text-sm\">\n                    AI analysis complete with {generatedInsights.key_findings.length} key findings and {generatedInsights.recommendations.length} recommendations.\n                  </p>\n                </div>\n\n                <div className=\"bg-white border rounded-lg p-6\">\n                  <h4 className=\"font-medium text-gray-900 mb-3\">📋 Executive Summary</h4>\n                  <p className=\"text-gray-700\">{generatedInsights.executive_summary}</p>\n                </div>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div className=\"bg-white border rounded-lg p-6\">\n                    <h4 className=\"font-medium text-gray-900 mb-3\">🔍 Key Findings</h4>\n                    <ul className=\"space-y-2 text-sm text-gray-700\">\n                      {generatedInsights.key_findings.slice(0, 3).map((finding: string, index: number) => (\n                        <li key={index} className=\"flex items-start\">\n                          <span className=\"text-blue-500 mr-2\">•</span>\n                          {finding}\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n\n                  <div className=\"bg-white border rounded-lg p-6\">\n                    <h4 className=\"font-medium text-gray-900 mb-3\">💡 Recommendations</h4>\n                    <ul className=\"space-y-2 text-sm text-gray-700\">\n                      {generatedInsights.recommendations.slice(0, 3).map((rec: string, index: number) => (\n                        <li key={index} className=\"flex items-start\">\n                          <span className=\"text-green-500 mr-2\">→</span>\n                          {rec}\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                </div>\n\n                <button\n                  onClick={() => setCurrentStep('customize')}\n                  className=\"bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors\"\n                >\n                  Next: Customize Report →\n                </button>\n              </div>\n            )}\n          </div>\n        );\n\n      case 'customize':\n        return (\n          <div className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Customize Report</h3>\n            \n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n              <div className=\"space-y-6\">\n                <div>\n                  <h4 className=\"font-medium text-gray-900 mb-3\">Report Format</h4>\n                  <div className=\"space-y-2\">\n                    {[\n                      { id: 'pdf', name: 'PDF Report', desc: 'Professional document for sharing' },\n                      { id: 'powerpoint', name: 'PowerPoint', desc: 'Presentation slides' },\n                      { id: 'dashboard', name: 'Interactive Dashboard', desc: 'Live web dashboard' },\n                    ].map((format) => (\n                      <label key={format.id} className=\"flex items-center p-3 border rounded cursor-pointer\">\n                        <input\n                          type=\"radio\"\n                          name=\"report_format\"\n                          value={format.id}\n                          checked={reportFormat === format.id}\n                          onChange={(e) => setReportFormat(e.target.value as any)}\n                          className=\"mr-3\"\n                        />\n                        <div>\n                          <div className=\"font-medium text-gray-900\">{format.name}</div>\n                          <div className=\"text-sm text-gray-600\">{format.desc}</div>\n                        </div>\n                      </label>\n                    ))}\n                  </div>\n                </div>\n\n                <div>\n                  <h4 className=\"font-medium text-gray-900 mb-3\">KPI Dashboard Preview</h4>\n                  <div className=\"grid grid-cols-2 gap-3\">\n                    {generatedInsights?.kpi_dashboard.slice(0, 4).map((kpi: any, index: number) => (\n                      <div key={index} className=\"p-3 bg-gray-50 rounded\">\n                        <div className=\"text-sm text-gray-600\">{kpi.name}</div>\n                        <div className=\"flex items-center\">\n                          <span className=\"text-lg font-bold text-gray-900\">{kpi.value.toFixed(1)}</span>\n                          <span className={`ml-2 text-sm ${\n                            kpi.trend === 'up' ? 'text-green-600' : 'text-red-600'\n                          }`}>\n                            {kpi.trend === 'up' ? '↗' : '↘'} {kpi.change}%\n                          </span>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"space-y-6\">\n                <div>\n                  <h4 className=\"font-medium text-gray-900 mb-3\">Report Preview</h4>\n                  <div className=\"border rounded-lg p-4 bg-gray-50 h-64 overflow-y-auto\">\n                    <div className=\"space-y-4 text-sm\">\n                      <div>\n                        <h5 className=\"font-medium\">Executive Summary</h5>\n                        <p className=\"text-gray-600 mt-1\">{generatedInsights?.executive_summary}</p>\n                      </div>\n                      <div>\n                        <h5 className=\"font-medium\">Key Findings</h5>\n                        <ul className=\"text-gray-600 mt-1 space-y-1\">\n                          {generatedInsights?.key_findings.slice(0, 2).map((finding: string, index: number) => (\n                            <li key={index}>• {finding}</li>\n                          ))}\n                        </ul>\n                      </div>\n                      <div>\n                        <h5 className=\"font-medium\">Recommendations</h5>\n                        <ul className=\"text-gray-600 mt-1 space-y-1\">\n                          {generatedInsights?.recommendations.slice(0, 2).map((rec: string, index: number) => (\n                            <li key={index}>→ {rec}</li>\n                          ))}\n                        </ul>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <div>\n                  <h4 className=\"font-medium text-gray-900 mb-3\">Sharing Options</h4>\n                  <div className=\"space-y-2\">\n                    <label className=\"flex items-center\">\n                      <input type=\"checkbox\" className=\"mr-2\" />\n                      <span className=\"text-sm\">Email to stakeholders</span>\n                    </label>\n                    <label className=\"flex items-center\">\n                      <input type=\"checkbox\" className=\"mr-2\" />\n                      <span className=\"text-sm\">Share via Slack</span>\n                    </label>\n                    <label className=\"flex items-center\">\n                      <input type=\"checkbox\" className=\"mr-2\" />\n                      <span className=\"text-sm\">Schedule recurring reports</span>\n                    </label>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex gap-4\">\n              <button\n                onClick={() => setCurrentStep('generate')}\n                className=\"bg-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-400 transition-colors\"\n              >\n                ← Back\n              </button>\n              <button\n                onClick={() => setCurrentStep('export')}\n                className=\"bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors\"\n              >\n                Next: Export & Share →\n              </button>\n            </div>\n          </div>\n        );\n\n      case 'export':\n        return (\n          <div className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Export & Share</h3>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n              <div className=\"bg-white border rounded-lg p-6 text-center\">\n                <div className=\"text-4xl mb-3\">📄</div>\n                <h4 className=\"font-medium text-gray-900 mb-2\">Download PDF</h4>\n                <p className=\"text-sm text-gray-600 mb-4\">Professional report document</p>\n                <button className=\"bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors\">\n                  Download PDF\n                </button>\n              </div>\n\n              <div className=\"bg-white border rounded-lg p-6 text-center\">\n                <div className=\"text-4xl mb-3\">📊</div>\n                <h4 className=\"font-medium text-gray-900 mb-2\">PowerPoint</h4>\n                <p className=\"text-sm text-gray-600 mb-4\">Presentation slides</p>\n                <button className=\"bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700 transition-colors\">\n                  Download PPTX\n                </button>\n              </div>\n\n              <div className=\"bg-white border rounded-lg p-6 text-center\">\n                <div className=\"text-4xl mb-3\">🌐</div>\n                <h4 className=\"font-medium text-gray-900 mb-2\">Live Dashboard</h4>\n                <p className=\"text-sm text-gray-600 mb-4\">Interactive web dashboard</p>\n                <button className=\"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors\">\n                  Create Dashboard\n                </button>\n              </div>\n            </div>\n\n            <div className=\"bg-white border rounded-lg p-6\">\n              <h4 className=\"font-medium text-gray-900 mb-4\">📧 Share Report</h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Email Recipients\n                  </label>\n                  <textarea\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n                    rows={3}\n                    placeholder=\"Enter email addresses separated by commas\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Message\n                  </label>\n                  <textarea\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n                    rows={3}\n                    placeholder=\"Optional message to include with the report\"\n                  />\n                </div>\n              </div>\n              <button className=\"mt-4 bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors\">\n                📧 Send Report\n              </button>\n            </div>\n\n            <div className=\"bg-green-50 border border-green-200 rounded-lg p-4\">\n              <h4 className=\"font-medium text-green-800 mb-2\">✅ Report Ready</h4>\n              <p className=\"text-green-700 text-sm\">\n                Your AI-generated insights report is ready for download and sharing. \n                The report includes executive summary, key findings, recommendations, and KPI dashboard.\n              </p>\n            </div>\n          </div>\n        );\n\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">📋 Insights & Reports</h2>\n        <p className=\"text-gray-600\">\n          Generate natural language insights, create audience-specific reports, and share findings with stakeholders.\n        </p>\n      </div>\n\n      {/* Progress Steps */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <div className=\"flex items-center justify-between mb-8\">\n          {steps.map((step, index) => (\n            <div key={step.id} className=\"flex items-center\">\n              <div\n                className={`\n                  flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors cursor-pointer\n                  ${step.completed || currentStep === step.id\n                    ? 'bg-indigo-600 border-indigo-600 text-white'\n                    : 'bg-white border-gray-300 text-gray-400'\n                  }\n                `}\n                onClick={() => setCurrentStep(step.id as any)}\n              >\n                {step.completed ? '✓' : step.icon}\n              </div>\n              <div className=\"ml-3 hidden md:block\">\n                <div className={`text-sm font-medium ${\n                  step.completed || currentStep === step.id ? 'text-indigo-600' : 'text-gray-400'\n                }`}>\n                  {step.label}\n                </div>\n              </div>\n              {index < steps.length - 1 && (\n                <div className={`w-12 h-0.5 mx-4 ${\n                  step.completed ? 'bg-indigo-600' : 'bg-gray-300'\n                }`} />\n              )}\n            </div>\n          ))}\n        </div>\n\n        {/* Step Content */}\n        {renderStepContent()}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;AA2FiB;;AAzFjB;AACA;;;AAHA;;;AASO,SAAS,gBAAgB,KAAwC;QAAxC,EAAE,cAAc,EAAwB,GAAxC;;IAC9B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuC;IACpF,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAChE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkD;IACzG,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QAAC;QAAW;QAAU;KAAO;IACxF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsC;IACrF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,YAAY;QAChB;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,OAAO;YACP,MAAM;gBAAC;gBAAW;gBAAiB;gBAAa;aAAkB;QACpE;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,OAAO;YACP,MAAM;gBAAC;gBAAgB;gBAAmB;gBAAQ;aAAuB;QAC3E;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,OAAO;YACP,MAAM;gBAAC;gBAAiB;gBAAe;gBAAoB;aAAwB;QACrF;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,OAAO;YACP,MAAM;gBAAC;gBAAsB;gBAAgB;gBAAkB;aAAiB;QAClF;KACD;IAED,MAAM,gBAAgB;QACpB;YAAE,IAAI;YAAW,MAAM;YAAiB,UAAU;QAAY;QAC9D;YAAE,IAAI;YAAU,MAAM;YAAe,UAAU;QAAY;QAC3D;YAAE,IAAI;YAAiB,MAAM;YAAiB,UAAU;QAAY;QACpE;YAAE,IAAI;YAAa,MAAM;YAAa,UAAU;QAAY;QAC5D;YAAE,IAAI;YAAmB,MAAM;YAAmB,UAAU;QAAc;QAC1E;YAAE,IAAI;YAAgB,MAAM;YAAgB,UAAU;QAAS;QAC/D;YAAE,IAAI;YAAmB,MAAM;YAAmB,UAAU;QAAS;QACrE;YAAE,IAAI;YAAQ,MAAM;YAAQ,UAAU;QAAS;QAC/C;YAAE,IAAI;YAAc,MAAM;YAAc,UAAU;QAAc;QAChE;YAAE,IAAI;YAAQ,MAAM;YAAmB,UAAU;QAAO;KACzD;IAED,MAAM,QAAQ;QACZ;YAAE,IAAI;YAAY,OAAO;YAAqB,MAAM;YAAM,WAAW,sBAAsB;QAAK;QAChG;YAAE,IAAI;YAAa,OAAO;YAAoB,MAAM;YAAM,WAAW;QAAM;QAC3E;YAAE,IAAI;YAAU,OAAO;YAAkB,MAAM;YAAM,WAAW;QAAM;KACvE;IAED,MAAM,mBAAmB;QACvB,IAAI;YACF,gBAAgB;YAEhB,2DAA2D;YAC3D,MAAM,CAAC,qBAAqB,mBAAmB,wBAAwB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC1F,6CAA6C;gBAC7C,oHAAA,CAAA,YAAS,CAAC,eAAe,CAAC;oBACxB,gBAAgB;wBAAC;wBAAuB;wBAA0B;qBAAqB;oBACvF,eAAe;oBACf,UAAU;gBACZ;gBACA,6CAA6C;gBAC7C,oHAAA,CAAA,YAAS,CAAC,sBAAsB,CAAC;oBAC/B,gBAAgB;wBACd,SAAS;wBACT,oBAAoB;wBACpB,OAAO;wBACP,WAAW;wBACX,MAAM;oBACR;oBACA,eAAe;gBACjB;gBACA,2CAA2C;gBAC3C,MAAM,AAAC,GAAkC,kEAAA,2BAAyB;oBAChE,QAAQ;oBACR,SAAS;wBAAE,gBAAgB;oBAAmB;oBAC9C,MAAM,KAAK,SAAS,CAAC;wBACnB,UAAU;wBACV,MAAM;wBACN,cAAc;4BAAC;4BAAW;4BAAS;yBAAQ;oBAC7C;gBACF;aACD;YAED,MAAM,WAAW;gBACf,eAAe,oBAAoB,IAAI;gBACvC,oBAAoB,kBAAkB,QAAQ;gBAC9C,kBAAkB,wBAAwB,EAAE,GAAG,MAAM,wBAAwB,IAAI,KAAK;gBACtF,mBAAmB,yBAAyB;gBAC5C,cAAc;gBACd,iBAAiB,wBAAwB;gBACzC,eAAe,qBAAqB;YACtC;YAEA,qBAAqB;YACrB,eAAe;YACf,2BAAA,qCAAA,eAAiB;QAEnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,2BAA2B,CAAC;QAChC,MAAM,YAAY;YAChB,KAAK;YACL,cAAc;YACd,OAAO;YACP,WAAW;QACb;QACA,OAAO,SAAS,CAAC,SAAmC;IACtD;IAEA,MAAM,sBAAsB,IAAM;YAChC;YACA;YACA;YACA;YACA;SACD;IAED,MAAM,0BAA0B,CAAC;QAC/B,MAAM,kBAAkB;YACtB,KAAK;gBACH;gBACA;gBACA;aACD;YACD,cAAc;gBACZ;gBACA;gBACA;aACD;YACD,OAAO;gBACL;gBACA;gBACA;aACD;YACD,WAAW;gBACT;gBACA;gBACA;aACD;QACH;QACA,OAAO,eAAe,CAAC,SAAyC;IAClE;IAEA,MAAM,uBAAuB,CAAC;QAC5B,OAAO,KAAK,GAAG,CAAC,CAAA;gBAER;mBAFgB;gBACtB,IAAI;gBACJ,MAAM,EAAA,sBAAA,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,kBAAjC,0CAAA,oBAAuC,IAAI,KAAI;gBACrD,OAAO,KAAK,MAAM,KAAK;gBACvB,OAAO,KAAK,MAAM,KAAK,MAAM,OAAO;gBACpC,QAAQ,CAAC,KAAK,MAAM,KAAK,KAAK,EAAE,EAAE,OAAO,CAAC;YAC5C;;IACF;IAEA,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAEpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAiC;;;;;;8CAC/C,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAKvC,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAiC;;;;;;8CAC/C,6LAAC;oCAAI,WAAU;8CACZ,UAAU,GAAG,CAAC,CAAC,yBACd,6LAAC;4CAEC,WAAW,AAAC,4DAIX,OAHC,qBAAqB,SAAS,EAAE,GAC5B,mCACA;4CAEN,SAAS,IAAM,oBAAoB,SAAS,EAAE;sDAE9C,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAiB,SAAS,IAAI;;;;;;kEAC9C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAA6B,SAAS,IAAI;;;;;;0EACxD,6LAAC;gEAAE,WAAU;0EAA8B,SAAS,KAAK;;;;;;0EACzD,6LAAC;gEAAI,WAAU;0EACZ,SAAS,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC9B,6LAAC;wEAAe,WAAU;kFACvB,IAAI,OAAO,CAAC,KAAK;uEADT;;;;;;;;;;;;;;;;;;;;;;2CAfd,SAAS,EAAE;;;;;;;;;;;;;;;;sCA2BxB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAiC;;;;;;8CAC/C,6LAAC;oCAAI,WAAU;8CACZ,cAAc,GAAG,CAAC,CAAC,oBAClB,6LAAC;4CAAmB,WAAU;;8DAC5B,6LAAC;oDACC,MAAK;oDACL,SAAS,aAAa,QAAQ,CAAC,IAAI,EAAE;oDACrC,UAAU,CAAC;wDACT,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE;4DACpB,gBAAgB;mEAAI;gEAAc,IAAI,EAAE;6DAAC;wDAC3C,OAAO;4DACL,gBAAgB,aAAa,MAAM,CAAC,CAAA,IAAK,MAAM,IAAI,EAAE;wDACvD;oDACF;oDACA,WAAU;;;;;;8DAEZ,6LAAC;oDAAK,WAAU;8DAAW,IAAI,IAAI;;;;;;;2CAbzB,IAAI,EAAE;;;;;;;;;;;;;;;;wBAmBvB,CAAC,kCACA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS;gCACT,UAAU,gBAAgB,aAAa,MAAM,KAAK;gCAClD,WAAU;0CAET,eAAe,8BAA8B;;;;;;;;;;iDAIlD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAE,WAAU;;gDAAyB;gDACT,kBAAkB,YAAY,CAAC,MAAM;gDAAC;gDAAmB,kBAAkB,eAAe,CAAC,MAAM;gDAAC;;;;;;;;;;;;;8CAIjI,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,6LAAC;4CAAE,WAAU;sDAAiB,kBAAkB,iBAAiB;;;;;;;;;;;;8CAGnE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAiC;;;;;;8DAC/C,6LAAC;oDAAG,WAAU;8DACX,kBAAkB,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAiB,sBAChE,6LAAC;4DAAe,WAAU;;8EACxB,6LAAC;oEAAK,WAAU;8EAAqB;;;;;;gEACpC;;2DAFM;;;;;;;;;;;;;;;;sDAQf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAiC;;;;;;8DAC/C,6LAAC;oDAAG,WAAU;8DACX,kBAAkB,eAAe,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAa,sBAC/D,6LAAC;4DAAe,WAAU;;8EACxB,6LAAC;oEAAK,WAAU;8EAAsB;;;;;;gEACrC;;2DAFM;;;;;;;;;;;;;;;;;;;;;;8CASjB,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CACX;;;;;;;;;;;;;;;;;;YAQX,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAEpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAiC;;;;;;8DAC/C,6LAAC;oDAAI,WAAU;8DACZ;wDACC;4DAAE,IAAI;4DAAO,MAAM;4DAAc,MAAM;wDAAoC;wDAC3E;4DAAE,IAAI;4DAAc,MAAM;4DAAc,MAAM;wDAAsB;wDACpE;4DAAE,IAAI;4DAAa,MAAM;4DAAyB,MAAM;wDAAqB;qDAC9E,CAAC,GAAG,CAAC,CAAC,uBACL,6LAAC;4DAAsB,WAAU;;8EAC/B,6LAAC;oEACC,MAAK;oEACL,MAAK;oEACL,OAAO,OAAO,EAAE;oEAChB,SAAS,iBAAiB,OAAO,EAAE;oEACnC,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oEAC/C,WAAU;;;;;;8EAEZ,6LAAC;;sFACC,6LAAC;4EAAI,WAAU;sFAA6B,OAAO,IAAI;;;;;;sFACvD,6LAAC;4EAAI,WAAU;sFAAyB,OAAO,IAAI;;;;;;;;;;;;;2DAX3C,OAAO,EAAE;;;;;;;;;;;;;;;;sDAkB3B,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAiC;;;;;;8DAC/C,6LAAC;oDAAI,WAAU;8DACZ,8BAAA,wCAAA,kBAAmB,aAAa,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAU,sBAC3D,6LAAC;4DAAgB,WAAU;;8EACzB,6LAAC;oEAAI,WAAU;8EAAyB,IAAI,IAAI;;;;;;8EAChD,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAmC,IAAI,KAAK,CAAC,OAAO,CAAC;;;;;;sFACrE,6LAAC;4EAAK,WAAW,AAAC,gBAEjB,OADC,IAAI,KAAK,KAAK,OAAO,mBAAmB;;gFAEvC,IAAI,KAAK,KAAK,OAAO,MAAM;gFAAI;gFAAE,IAAI,MAAM;gFAAC;;;;;;;;;;;;;;2DAPzC;;;;;;;;;;;;;;;;;;;;;;8CAgBlB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAiC;;;;;;8DAC/C,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFAAc;;;;;;kFAC5B,6LAAC;wEAAE,WAAU;kFAAsB,8BAAA,wCAAA,kBAAmB,iBAAiB;;;;;;;;;;;;0EAEzE,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFAAc;;;;;;kFAC5B,6LAAC;wEAAG,WAAU;kFACX,8BAAA,wCAAA,kBAAmB,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAiB,sBACjE,6LAAC;;oFAAe;oFAAG;;+EAAV;;;;;;;;;;;;;;;;0EAIf,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFAAc;;;;;;kFAC5B,6LAAC;wEAAG,WAAU;kFACX,8BAAA,wCAAA,kBAAmB,eAAe,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAa,sBAChE,6LAAC;;oFAAe;oFAAG;;+EAAV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAQrB,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAiC;;;;;;8DAC/C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAM,WAAU;;8EACf,6LAAC;oEAAM,MAAK;oEAAW,WAAU;;;;;;8EACjC,6LAAC;oEAAK,WAAU;8EAAU;;;;;;;;;;;;sEAE5B,6LAAC;4DAAM,WAAU;;8EACf,6LAAC;oEAAM,MAAK;oEAAW,WAAU;;;;;;8EACjC,6LAAC;oEAAK,WAAU;8EAAU;;;;;;;;;;;;sEAE5B,6LAAC;4DAAM,WAAU;;8EACf,6LAAC;oEAAM,MAAK;oEAAW,WAAU;;;;;;8EACjC,6LAAC;oEAAK,WAAU;8EAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOpC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CACX;;;;;;;;;;;;;;;;;;YAOT,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAEpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,6LAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAC1C,6LAAC;4CAAO,WAAU;sDAA6E;;;;;;;;;;;;8CAKjG,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,6LAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAC1C,6LAAC;4CAAO,WAAU;sDAAmF;;;;;;;;;;;;8CAKvG,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,6LAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAC1C,6LAAC;4CAAO,WAAU;sDAA+E;;;;;;;;;;;;;;;;;;sCAMrG,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAiC;;;;;;8CAC/C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,WAAU;oDACV,MAAM;oDACN,aAAY;;;;;;;;;;;;sDAGhB,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,WAAU;oDACV,MAAM;oDACN,aAAY;;;;;;;;;;;;;;;;;;8CAIlB,6LAAC;oCAAO,WAAU;8CAAyF;;;;;;;;;;;;sCAK7G,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAkC;;;;;;8CAChD,6LAAC;oCAAE,WAAU;8CAAyB;;;;;;;;;;;;;;;;;;YAQ9C;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAM/B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;gCAAkB,WAAU;;kDAC3B,6LAAC;wCACC,WAAW,AAAC,4IAKT,OAHC,KAAK,SAAS,IAAI,gBAAgB,KAAK,EAAE,GACvC,+CACA,0CACH;wCAEH,SAAS,IAAM,eAAe,KAAK,EAAE;kDAEpC,KAAK,SAAS,GAAG,MAAM,KAAK,IAAI;;;;;;kDAEnC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAW,AAAC,uBAEhB,OADC,KAAK,SAAS,IAAI,gBAAgB,KAAK,EAAE,GAAG,oBAAoB;sDAE/D,KAAK,KAAK;;;;;;;;;;;oCAGd,QAAQ,MAAM,MAAM,GAAG,mBACtB,6LAAC;wCAAI,WAAW,AAAC,mBAEhB,OADC,KAAK,SAAS,GAAG,kBAAkB;;;;;;;+BAtB/B,KAAK,EAAE;;;;;;;;;;oBA8BpB;;;;;;;;;;;;;AAIT;GA/hBgB;KAAA", "debugId": null}}, {"offset": {"line": 10229, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/src/components/advanced-analytics.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { apiClient } from '@/lib/api';\n\ninterface AdvancedAnalyticsProps {\n  onAnalysisUpdate?: (analysis: any) => void;\n}\n\nexport function AdvancedAnalytics({ onAnalysisUpdate }: AdvancedAnalyticsProps) {\n  const [selectedAnalysis, setSelectedAnalysis] = useState<string>('');\n  const [analysisResults, setAnalysisResults] = useState<any>(null);\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [analysisHistory, setAnalysisHistory] = useState<any[]>([]);\n\n  const analysisTypes = [\n    {\n      id: 'bayesian_forecasting',\n      name: 'Bayesian Forecasting',\n      icon: '🎯',\n      description: 'Advanced probabilistic forecasting with credible intervals',\n      capabilities: ['State-space models', 'Hierarchical Bayesian', 'Change-point detection'],\n      backend: 'BayesianForecastingEngine',\n      complexity: 'Advanced',\n    },\n    {\n      id: 'elasticity_modeling',\n      name: 'Elasticity Analysis',\n      icon: '📈',\n      description: 'Price and income elasticity modeling for strategic decisions',\n      capabilities: ['Price sensitivity', 'Economic elasticity', 'Market response'],\n      backend: 'ElasticityModeler',\n      complexity: 'Intermediate',\n    },\n    {\n      id: 'risk_portfolio',\n      name: 'Risk Portfolio Optimization',\n      icon: '⚖️',\n      description: 'Advanced portfolio optimization with VaR calculations',\n      capabilities: ['Portfolio optimization', 'Value at Risk', 'Risk-return analysis'],\n      backend: 'RiskAnalyzer',\n      complexity: 'Advanced',\n    },\n    {\n      id: 'market_intelligence',\n      name: 'Market Intelligence',\n      icon: '🔍',\n      description: 'AI-powered market research and competitive analysis',\n      capabilities: ['Competitor analysis', 'Market trends', 'Regulatory monitoring'],\n      backend: 'WebResearchAgent',\n      complexity: 'Intermediate',\n    },\n    {\n      id: 'cost_optimization',\n      name: 'Cost Optimization',\n      icon: '💰',\n      description: 'Advanced cost analysis and optimization recommendations',\n      capabilities: ['Cost driver analysis', 'Optimization algorithms', 'Efficiency metrics'],\n      backend: 'FixedCostAgent',\n      complexity: 'Intermediate',\n    },\n    {\n      id: 'time_series_deep',\n      name: 'Deep Time Series Analysis',\n      icon: '📊',\n      description: 'Comprehensive time series analysis with business insights',\n      capabilities: ['Trend decomposition', 'Seasonality analysis', 'Anomaly detection'],\n      backend: 'TimeSeriesAnalyzer',\n      complexity: 'Advanced',\n    },\n  ];\n\n  const runAdvancedAnalysis = async (analysisType: string) => {\n    try {\n      setIsAnalyzing(true);\n      setSelectedAnalysis(analysisType);\n      \n      let response;\n      \n      switch (analysisType) {\n        case 'bayesian_forecasting':\n          response = await runBayesianForecasting();\n          break;\n        case 'elasticity_modeling':\n          response = await runElasticityAnalysis();\n          break;\n        case 'risk_portfolio':\n          response = await runRiskPortfolioAnalysis();\n          break;\n        case 'market_intelligence':\n          response = await runMarketIntelligence();\n          break;\n        case 'cost_optimization':\n          response = await runCostOptimization();\n          break;\n        case 'time_series_deep':\n          response = await runDeepTimeSeriesAnalysis();\n          break;\n        default:\n          throw new Error('Unknown analysis type');\n      }\n      \n      setAnalysisResults(response);\n      \n      // Add to history\n      const historyEntry = {\n        id: Date.now(),\n        type: analysisType,\n        name: analysisTypes.find(a => a.id === analysisType)?.name,\n        timestamp: new Date().toISOString(),\n        results: response,\n      };\n      setAnalysisHistory([historyEntry, ...analysisHistory.slice(0, 4)]);\n      \n      onAnalysisUpdate?.(response);\n      \n    } catch (error) {\n      console.error('Advanced analysis error:', error);\n    } finally {\n      setIsAnalyzing(false);\n    }\n  };\n\n  const runBayesianForecasting = async () => {\n    // Simulate advanced Bayesian forecasting\n    return {\n      method: 'Bayesian State-Space Model',\n      forecast_periods: 12,\n      credible_intervals: {\n        '50%': [145000000, 155000000],\n        '80%': [140000000, 160000000],\n        '95%': [135000000, 165000000],\n      },\n      posterior_samples: 4000,\n      model_diagnostics: {\n        r_hat: 1.01,\n        effective_sample_size: 3800,\n        mcmc_convergence: 'Excellent',\n      },\n      change_points: [\n        { period: 'Q2 2024', probability: 0.85, description: 'Market expansion impact' },\n        { period: 'Q4 2024', probability: 0.72, description: 'Seasonal adjustment' },\n      ],\n      business_insights: [\n        'High probability (85%) of revenue acceleration in Q2 2024',\n        'Model suggests sustainable growth pattern with low volatility',\n        'Credible intervals indicate 95% confidence in $135M-$165M range',\n      ],\n    };\n  };\n\n  const runElasticityAnalysis = async () => {\n    return {\n      price_elasticity: {\n        value: -1.2,\n        interpretation: 'Elastic demand - 1% price increase leads to 1.2% demand decrease',\n        confidence_interval: [-1.5, -0.9],\n        business_impact: 'Price sensitive market - focus on value proposition',\n      },\n      income_elasticity: {\n        value: 1.8,\n        interpretation: 'Luxury good behavior - 1% income increase leads to 1.8% demand increase',\n        confidence_interval: [1.5, 2.1],\n        business_impact: 'Strong correlation with economic growth',\n      },\n      cross_elasticity: {\n        competitors: [\n          { name: 'Competitor A', elasticity: 0.6, relationship: 'Substitute' },\n          { name: 'Competitor B', elasticity: -0.3, relationship: 'Complement' },\n        ],\n      },\n      strategic_recommendations: [\n        'Consider premium pricing strategy in high-income segments',\n        'Implement dynamic pricing based on economic indicators',\n        'Focus on differentiation to reduce price sensitivity',\n      ],\n    };\n  };\n\n  const runRiskPortfolioAnalysis = async () => {\n    return {\n      portfolio_optimization: {\n        optimal_allocation: {\n          'Virgin Mobile Chile': 0.25,\n          'Virgin Mobile Colombia': 0.20,\n          'FriendyMobile Oman': 0.15,\n          'FriendiPay KSA': 0.25,\n          'Virgin Connect Roam': 0.15,\n        },\n        expected_return: 0.156,\n        portfolio_risk: 0.089,\n        sharpe_ratio: 1.75,\n      },\n      value_at_risk: {\n        '1_day_95%': 2500000,\n        '1_day_99%': 4200000,\n        '10_day_95%': 7900000,\n        '10_day_99%': 13300000,\n      },\n      risk_decomposition: {\n        market_risk: 0.65,\n        credit_risk: 0.20,\n        operational_risk: 0.10,\n        regulatory_risk: 0.05,\n      },\n      stress_testing: {\n        scenarios: [\n          { name: 'Economic Recession', impact: -0.15, probability: 0.20 },\n          { name: 'Regulatory Changes', impact: -0.08, probability: 0.35 },\n          { name: 'Competitive Disruption', impact: -0.12, probability: 0.25 },\n        ],\n      },\n    };\n  };\n\n  const runMarketIntelligence = async () => {\n    return {\n      competitive_landscape: {\n        market_leaders: [\n          { name: 'Competitor A', market_share: 0.35, trend: 'stable' },\n          { name: 'Competitor B', market_share: 0.28, trend: 'declining' },\n          { name: 'Your Company', market_share: 0.22, trend: 'growing' },\n        ],\n        competitive_advantages: [\n          'Superior customer service ratings',\n          'Innovative digital platform',\n          'Strong brand recognition in key markets',\n        ],\n      },\n      market_trends: [\n        { trend: '5G Adoption', impact: 'High', timeline: '2024-2025' },\n        { trend: 'Digital Payment Growth', impact: 'Medium', timeline: '2024' },\n        { trend: 'Regulatory Consolidation', impact: 'High', timeline: '2025' },\n      ],\n      regulatory_monitoring: [\n        { regulation: 'Data Privacy Laws', status: 'Proposed', impact: 'Medium' },\n        { regulation: 'Telecom Licensing', status: 'Under Review', impact: 'High' },\n      ],\n      strategic_opportunities: [\n        'Market expansion in underserved regions',\n        'Partnership opportunities with fintech companies',\n        'Technology acquisition targets identified',\n      ],\n    };\n  };\n\n  const runCostOptimization = async () => {\n    return {\n      cost_analysis: {\n        total_costs: 125000000,\n        cost_categories: {\n          personnel: { amount: 45000000, percentage: 0.36, optimization_potential: 0.08 },\n          technology: { amount: 35000000, percentage: 0.28, optimization_potential: 0.15 },\n          marketing: { amount: 25000000, percentage: 0.20, optimization_potential: 0.12 },\n          operations: { amount: 20000000, percentage: 0.16, optimization_potential: 0.10 },\n        },\n      },\n      optimization_opportunities: [\n        {\n          category: 'Technology',\n          opportunity: 'Cloud migration',\n          potential_savings: 5250000,\n          implementation_cost: 1200000,\n          payback_period: '4 months',\n          risk_level: 'Low',\n        },\n        {\n          category: 'Marketing',\n          opportunity: 'Digital channel optimization',\n          potential_savings: 3000000,\n          implementation_cost: 500000,\n          payback_period: '2 months',\n          risk_level: 'Low',\n        },\n      ],\n      efficiency_metrics: {\n        cost_per_customer: 50,\n        cost_per_revenue_dollar: 0.83,\n        operational_efficiency_score: 0.76,\n      },\n      recommendations: [\n        'Prioritize cloud migration for immediate cost savings',\n        'Implement automated customer service to reduce personnel costs',\n        'Consolidate vendor relationships for better pricing',\n      ],\n    };\n  };\n\n  const runDeepTimeSeriesAnalysis = async () => {\n    return {\n      trend_analysis: {\n        overall_trend: 'Positive',\n        trend_strength: 0.78,\n        trend_components: {\n          linear: 0.65,\n          exponential: 0.23,\n          polynomial: 0.12,\n        },\n      },\n      seasonality: {\n        seasonal_strength: 0.45,\n        seasonal_periods: [12, 4], // Monthly and quarterly\n        peak_periods: ['Q4', 'Q1'],\n        trough_periods: ['Q2', 'Q3'],\n      },\n      anomaly_detection: [\n        { period: '2023-08', type: 'Positive Outlier', magnitude: 2.3, explanation: 'Marketing campaign impact' },\n        { period: '2023-11', type: 'Negative Outlier', magnitude: -1.8, explanation: 'System outage' },\n      ],\n      forecast_accuracy: {\n        mape: 0.087, // Mean Absolute Percentage Error\n        rmse: 2450000, // Root Mean Square Error\n        mae: 1890000, // Mean Absolute Error\n      },\n      business_insights: [\n        'Strong underlying growth trend with 7.8% annual increase',\n        'Seasonal patterns suggest Q4 revenue boost of 15%',\n        'Model accuracy of 91.3% provides high confidence in forecasts',\n      ],\n    };\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">🔬 Advanced Analytics</h2>\n        <p className=\"text-gray-600\">\n          Sophisticated analytical models including Bayesian forecasting, elasticity analysis, and risk optimization.\n        </p>\n      </div>\n\n      {/* Analysis Selection */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Select Advanced Analysis</h3>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {analysisTypes.map((analysis) => (\n            <div\n              key={analysis.id}\n              className={`p-6 border-2 rounded-lg cursor-pointer transition-colors ${\n                selectedAnalysis === analysis.id\n                  ? 'border-indigo-500 bg-indigo-50'\n                  : 'border-gray-200 hover:border-gray-300'\n              }`}\n              onClick={() => runAdvancedAnalysis(analysis.id)}\n            >\n              <div className=\"text-center mb-4\">\n                <div className=\"text-4xl mb-2\">{analysis.icon}</div>\n                <h4 className=\"font-semibold text-gray-900\">{analysis.name}</h4>\n                <span className={`inline-block px-2 py-1 text-xs rounded-full mt-2 ${\n                  analysis.complexity === 'Advanced' ? 'bg-red-100 text-red-800' :\n                  'bg-yellow-100 text-yellow-800'\n                }`}>\n                  {analysis.complexity}\n                </span>\n              </div>\n              \n              <p className=\"text-sm text-gray-600 mb-3\">{analysis.description}</p>\n              \n              <div className=\"space-y-1\">\n                {analysis.capabilities.map((capability, index) => (\n                  <div key={index} className=\"text-xs text-gray-500 flex items-center\">\n                    <span className=\"w-1 h-1 bg-gray-400 rounded-full mr-2\"></span>\n                    {capability}\n                  </div>\n                ))}\n              </div>\n              \n              <div className=\"mt-3 pt-3 border-t border-gray-200\">\n                <div className=\"text-xs text-gray-500\">\n                  Backend: <span className=\"font-medium\">{analysis.backend}</span>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Analysis Results */}\n      {isAnalyzing && (\n        <div className=\"bg-white rounded-lg shadow-sm p-6\">\n          <div className=\"text-center py-8\">\n            <div className=\"text-4xl mb-4\">🔬</div>\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Running Advanced Analysis</h3>\n            <p className=\"text-gray-600\">\n              {analysisTypes.find(a => a.id === selectedAnalysis)?.name} in progress...\n            </p>\n            <div className=\"mt-4\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto\"></div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {analysisResults && !isAnalyzing && (\n        <div className=\"bg-white rounded-lg shadow-sm p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n            {analysisTypes.find(a => a.id === selectedAnalysis)?.name} Results\n          </h3>\n          \n          <div className=\"bg-gray-50 rounded-lg p-4 mb-6\">\n            <pre className=\"text-sm text-gray-700 overflow-auto max-h-96\">\n              {JSON.stringify(analysisResults, null, 2)}\n            </pre>\n          </div>\n          \n          <div className=\"flex gap-4\">\n            <button\n              onClick={() => {\n                setAnalysisResults(null);\n                setSelectedAnalysis('');\n              }}\n              className=\"bg-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-400 transition-colors\"\n            >\n              Run Another Analysis\n            </button>\n            <button className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\">\n              Export Results\n            </button>\n          </div>\n        </div>\n      )}\n\n      {/* Analysis History */}\n      {analysisHistory.length > 0 && (\n        <div className=\"bg-white rounded-lg shadow-sm p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Recent Analysis History</h3>\n          \n          <div className=\"space-y-3\">\n            {analysisHistory.map((entry) => (\n              <div key={entry.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded\">\n                <div>\n                  <h4 className=\"font-medium text-gray-900\">{entry.name}</h4>\n                  <p className=\"text-sm text-gray-600\">\n                    {new Date(entry.timestamp).toLocaleString()}\n                  </p>\n                </div>\n                <button\n                  onClick={() => setAnalysisResults(entry.results)}\n                  className=\"text-indigo-600 hover:text-indigo-800 text-sm font-medium\"\n                >\n                  View Results\n                </button>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AASO,SAAS,kBAAkB,KAA4C;QAA5C,EAAE,gBAAgB,EAA0B,GAA5C;QAyXnB,qBAYF;;IApYX,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAC5D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAEhE,MAAM,gBAAgB;QACpB;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;YACb,cAAc;gBAAC;gBAAsB;gBAAyB;aAAyB;YACvF,SAAS;YACT,YAAY;QACd;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;YACb,cAAc;gBAAC;gBAAqB;gBAAuB;aAAkB;YAC7E,SAAS;YACT,YAAY;QACd;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;YACb,cAAc;gBAAC;gBAA0B;gBAAiB;aAAuB;YACjF,SAAS;YACT,YAAY;QACd;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;YACb,cAAc;gBAAC;gBAAuB;gBAAiB;aAAwB;YAC/E,SAAS;YACT,YAAY;QACd;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;YACb,cAAc;gBAAC;gBAAwB;gBAA2B;aAAqB;YACvF,SAAS;YACT,YAAY;QACd;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;YACb,cAAc;gBAAC;gBAAuB;gBAAwB;aAAoB;YAClF,SAAS;YACT,YAAY;QACd;KACD;IAED,MAAM,sBAAsB,OAAO;QACjC,IAAI;gBAmCM;YAlCR,eAAe;YACf,oBAAoB;YAEpB,IAAI;YAEJ,OAAQ;gBACN,KAAK;oBACH,WAAW,MAAM;oBACjB;gBACF,KAAK;oBACH,WAAW,MAAM;oBACjB;gBACF,KAAK;oBACH,WAAW,MAAM;oBACjB;gBACF,KAAK;oBACH,WAAW,MAAM;oBACjB;gBACF,KAAK;oBACH,WAAW,MAAM;oBACjB;gBACF,KAAK;oBACH,WAAW,MAAM;oBACjB;gBACF;oBACE,MAAM,IAAI,MAAM;YACpB;YAEA,mBAAmB;YAEnB,iBAAiB;YACjB,MAAM,eAAe;gBACnB,IAAI,KAAK,GAAG;gBACZ,MAAM;gBACN,IAAI,GAAE,sBAAA,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,2BAAjC,0CAAA,oBAAgD,IAAI;gBAC1D,WAAW,IAAI,OAAO,WAAW;gBACjC,SAAS;YACX;YACA,mBAAmB;gBAAC;mBAAiB,gBAAgB,KAAK,CAAC,GAAG;aAAG;YAEjE,6BAAA,uCAAA,iBAAmB;QAErB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,yBAAyB;QAC7B,yCAAyC;QACzC,OAAO;YACL,QAAQ;YACR,kBAAkB;YAClB,oBAAoB;gBAClB,OAAO;oBAAC;oBAAW;iBAAU;gBAC7B,OAAO;oBAAC;oBAAW;iBAAU;gBAC7B,OAAO;oBAAC;oBAAW;iBAAU;YAC/B;YACA,mBAAmB;YACnB,mBAAmB;gBACjB,OAAO;gBACP,uBAAuB;gBACvB,kBAAkB;YACpB;YACA,eAAe;gBACb;oBAAE,QAAQ;oBAAW,aAAa;oBAAM,aAAa;gBAA0B;gBAC/E;oBAAE,QAAQ;oBAAW,aAAa;oBAAM,aAAa;gBAAsB;aAC5E;YACD,mBAAmB;gBACjB;gBACA;gBACA;aACD;QACH;IACF;IAEA,MAAM,wBAAwB;QAC5B,OAAO;YACL,kBAAkB;gBAChB,OAAO,CAAC;gBACR,gBAAgB;gBAChB,qBAAqB;oBAAC,CAAC;oBAAK,CAAC;iBAAI;gBACjC,iBAAiB;YACnB;YACA,mBAAmB;gBACjB,OAAO;gBACP,gBAAgB;gBAChB,qBAAqB;oBAAC;oBAAK;iBAAI;gBAC/B,iBAAiB;YACnB;YACA,kBAAkB;gBAChB,aAAa;oBACX;wBAAE,MAAM;wBAAgB,YAAY;wBAAK,cAAc;oBAAa;oBACpE;wBAAE,MAAM;wBAAgB,YAAY,CAAC;wBAAK,cAAc;oBAAa;iBACtE;YACH;YACA,2BAA2B;gBACzB;gBACA;gBACA;aACD;QACH;IACF;IAEA,MAAM,2BAA2B;QAC/B,OAAO;YACL,wBAAwB;gBACtB,oBAAoB;oBAClB,uBAAuB;oBACvB,0BAA0B;oBAC1B,sBAAsB;oBACtB,kBAAkB;oBAClB,uBAAuB;gBACzB;gBACA,iBAAiB;gBACjB,gBAAgB;gBAChB,cAAc;YAChB;YACA,eAAe;gBACb,aAAa;gBACb,aAAa;gBACb,cAAc;gBACd,cAAc;YAChB;YACA,oBAAoB;gBAClB,aAAa;gBACb,aAAa;gBACb,kBAAkB;gBAClB,iBAAiB;YACnB;YACA,gBAAgB;gBACd,WAAW;oBACT;wBAAE,MAAM;wBAAsB,QAAQ,CAAC;wBAAM,aAAa;oBAAK;oBAC/D;wBAAE,MAAM;wBAAsB,QAAQ,CAAC;wBAAM,aAAa;oBAAK;oBAC/D;wBAAE,MAAM;wBAA0B,QAAQ,CAAC;wBAAM,aAAa;oBAAK;iBACpE;YACH;QACF;IACF;IAEA,MAAM,wBAAwB;QAC5B,OAAO;YACL,uBAAuB;gBACrB,gBAAgB;oBACd;wBAAE,MAAM;wBAAgB,cAAc;wBAAM,OAAO;oBAAS;oBAC5D;wBAAE,MAAM;wBAAgB,cAAc;wBAAM,OAAO;oBAAY;oBAC/D;wBAAE,MAAM;wBAAgB,cAAc;wBAAM,OAAO;oBAAU;iBAC9D;gBACD,wBAAwB;oBACtB;oBACA;oBACA;iBACD;YACH;YACA,eAAe;gBACb;oBAAE,OAAO;oBAAe,QAAQ;oBAAQ,UAAU;gBAAY;gBAC9D;oBAAE,OAAO;oBAA0B,QAAQ;oBAAU,UAAU;gBAAO;gBACtE;oBAAE,OAAO;oBAA4B,QAAQ;oBAAQ,UAAU;gBAAO;aACvE;YACD,uBAAuB;gBACrB;oBAAE,YAAY;oBAAqB,QAAQ;oBAAY,QAAQ;gBAAS;gBACxE;oBAAE,YAAY;oBAAqB,QAAQ;oBAAgB,QAAQ;gBAAO;aAC3E;YACD,yBAAyB;gBACvB;gBACA;gBACA;aACD;QACH;IACF;IAEA,MAAM,sBAAsB;QAC1B,OAAO;YACL,eAAe;gBACb,aAAa;gBACb,iBAAiB;oBACf,WAAW;wBAAE,QAAQ;wBAAU,YAAY;wBAAM,wBAAwB;oBAAK;oBAC9E,YAAY;wBAAE,QAAQ;wBAAU,YAAY;wBAAM,wBAAwB;oBAAK;oBAC/E,WAAW;wBAAE,QAAQ;wBAAU,YAAY;wBAAM,wBAAwB;oBAAK;oBAC9E,YAAY;wBAAE,QAAQ;wBAAU,YAAY;wBAAM,wBAAwB;oBAAK;gBACjF;YACF;YACA,4BAA4B;gBAC1B;oBACE,UAAU;oBACV,aAAa;oBACb,mBAAmB;oBACnB,qBAAqB;oBACrB,gBAAgB;oBAChB,YAAY;gBACd;gBACA;oBACE,UAAU;oBACV,aAAa;oBACb,mBAAmB;oBACnB,qBAAqB;oBACrB,gBAAgB;oBAChB,YAAY;gBACd;aACD;YACD,oBAAoB;gBAClB,mBAAmB;gBACnB,yBAAyB;gBACzB,8BAA8B;YAChC;YACA,iBAAiB;gBACf;gBACA;gBACA;aACD;QACH;IACF;IAEA,MAAM,4BAA4B;QAChC,OAAO;YACL,gBAAgB;gBACd,eAAe;gBACf,gBAAgB;gBAChB,kBAAkB;oBAChB,QAAQ;oBACR,aAAa;oBACb,YAAY;gBACd;YACF;YACA,aAAa;gBACX,mBAAmB;gBACnB,kBAAkB;oBAAC;oBAAI;iBAAE;gBACzB,cAAc;oBAAC;oBAAM;iBAAK;gBAC1B,gBAAgB;oBAAC;oBAAM;iBAAK;YAC9B;YACA,mBAAmB;gBACjB;oBAAE,QAAQ;oBAAW,MAAM;oBAAoB,WAAW;oBAAK,aAAa;gBAA4B;gBACxG;oBAAE,QAAQ;oBAAW,MAAM;oBAAoB,WAAW,CAAC;oBAAK,aAAa;gBAAgB;aAC9F;YACD,mBAAmB;gBACjB,MAAM;gBACN,MAAM;gBACN,KAAK;YACP;YACA,mBAAmB;gBACjB;gBACA;gBACA;aACD;QACH;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAM/B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAEzD,6LAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,yBAClB,6LAAC;gCAEC,WAAW,AAAC,4DAIX,OAHC,qBAAqB,SAAS,EAAE,GAC5B,mCACA;gCAEN,SAAS,IAAM,oBAAoB,SAAS,EAAE;;kDAE9C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAiB,SAAS,IAAI;;;;;;0DAC7C,6LAAC;gDAAG,WAAU;0DAA+B,SAAS,IAAI;;;;;;0DAC1D,6LAAC;gDAAK,WAAW,AAAC,oDAGjB,OAFC,SAAS,UAAU,KAAK,aAAa,4BACrC;0DAEC,SAAS,UAAU;;;;;;;;;;;;kDAIxB,6LAAC;wCAAE,WAAU;kDAA8B,SAAS,WAAW;;;;;;kDAE/D,6LAAC;wCAAI,WAAU;kDACZ,SAAS,YAAY,CAAC,GAAG,CAAC,CAAC,YAAY,sBACtC,6LAAC;gDAAgB,WAAU;;kEACzB,6LAAC;wDAAK,WAAU;;;;;;oDACf;;+CAFO;;;;;;;;;;kDAOd,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;gDAAwB;8DAC5B,6LAAC;oDAAK,WAAU;8DAAe,SAAS,OAAO;;;;;;;;;;;;;;;;;;+BAhCvD,SAAS,EAAE;;;;;;;;;;;;;;;;YAyCvB,6BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,6LAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,6LAAC;4BAAE,WAAU;;iCACV,sBAAA,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,+BAAjC,0CAAA,oBAAoD,IAAI;gCAAC;;;;;;;sCAE5D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;YAMtB,mBAAmB,CAAC,6BACnB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;6BACX,uBAAA,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,+BAAjC,2CAAA,qBAAoD,IAAI;4BAAC;;;;;;;kCAG5D,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,KAAK,SAAS,CAAC,iBAAiB,MAAM;;;;;;;;;;;kCAI3C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;oCACP,mBAAmB;oCACnB,oBAAoB;gCACtB;gCACA,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCAAO,WAAU;0CAAkF;;;;;;;;;;;;;;;;;;YAQzG,gBAAgB,MAAM,GAAG,mBACxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAEzD,6LAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC,sBACpB,6LAAC;gCAAmB,WAAU;;kDAC5B,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA6B,MAAM,IAAI;;;;;;0DACrD,6LAAC;gDAAE,WAAU;0DACV,IAAI,KAAK,MAAM,SAAS,EAAE,cAAc;;;;;;;;;;;;kDAG7C,6LAAC;wCACC,SAAS,IAAM,mBAAmB,MAAM,OAAO;wCAC/C,WAAU;kDACX;;;;;;;+BAVO,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;AAoBhC;GA1bgB;KAAA", "debugId": null}}, {"offset": {"line": 11036, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/src/components/stream-navigation.tsx"], "sourcesContent": ["'use client';\n\ninterface StreamNavigationProps {\n  currentStream: 'dashboard' | 'data' | 'scenarios' | 'capex' | 'revenue' | 'insights' | 'advanced';\n  onStreamChange: (stream: 'dashboard' | 'data' | 'scenarios' | 'capex' | 'revenue' | 'insights' | 'advanced') => void;\n}\n\nexport function StreamNavigation({ currentStream, onStreamChange }: StreamNavigationProps) {\n  const streams = [\n    { \n      id: 'dashboard', \n      label: 'Dashboard', \n      icon: '📊',\n      description: 'Overview and quick actions'\n    },\n    { \n      id: 'data', \n      label: 'Data Integration', \n      icon: '📁',\n      description: 'Upload, validate & enrich data',\n      workflow: 'Stream 1'\n    },\n    { \n      id: 'scenarios', \n      label: 'Scenario Planning', \n      icon: '🎯',\n      description: 'Define constraints & run simulations',\n      workflow: 'Stream 2'\n    },\n    { \n      id: 'capex', \n      label: 'CAPEX Optimization', \n      icon: '🏗️',\n      description: 'Prioritize & optimize investments',\n      workflow: 'Stream 3'\n    },\n    { \n      id: 'revenue', \n      label: 'Revenue Forecasting', \n      icon: '📈',\n      description: 'Predict revenue & analyze drivers',\n      workflow: 'Stream 4'\n    },\n    {\n      id: 'insights',\n      label: 'Insights & Reports',\n      icon: '📋',\n      description: 'Generate insights & create reports',\n      workflow: 'Stream 5'\n    },\n    {\n      id: 'advanced',\n      label: 'Advanced Analytics',\n      icon: '🔬',\n      description: 'Sophisticated models & deep analysis',\n      workflow: 'Stream 6'\n    },\n  ] as const;\n\n  return (\n    <div className=\"bg-white shadow-sm border-b\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Main Navigation */}\n        <div className=\"flex space-x-8 overflow-x-auto\">\n          {streams.map((stream) => (\n            <button\n              key={stream.id}\n              onClick={() => onStreamChange(stream.id)}\n              className={`\n                flex flex-col items-center py-4 px-3 border-b-2 font-medium text-sm transition-colors min-w-max\n                ${currentStream === stream.id\n                  ? 'border-indigo-500 text-indigo-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }\n              `}\n            >\n              <span className=\"text-2xl mb-1\">{stream.icon}</span>\n              <span className=\"font-semibold\">{stream.label}</span>\n              {stream.workflow && (\n                <span className=\"text-xs text-gray-400 mt-1\">{stream.workflow}</span>\n              )}\n            </button>\n          ))}\n        </div>\n\n        {/* Stream Description */}\n        <div className=\"py-3 border-t border-gray-100\">\n          {streams.map((stream) => (\n            currentStream === stream.id && (\n              <div key={stream.id} className=\"flex items-center text-sm text-gray-600\">\n                <span className=\"mr-2\">{stream.icon}</span>\n                <span>{stream.description}</span>\n                {stream.workflow && (\n                  <span className=\"ml-4 px-2 py-1 bg-indigo-100 text-indigo-700 rounded-full text-xs\">\n                    {stream.workflow}\n                  </span>\n                )}\n              </div>\n            )\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAOO,SAAS,iBAAiB,KAAwD;QAAxD,EAAE,aAAa,EAAE,cAAc,EAAyB,GAAxD;IAC/B,MAAM,UAAU;QACd;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,aAAa;YACb,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,aAAa;YACb,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,aAAa;YACb,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,aAAa;YACb,UAAU;QACZ;QACA;YACE,IAAI;YAC<PERSON>,OAAO;YAC<PERSON>,MAAM;YAC<PERSON>,aAAa;<PERSON>AC<PERSON>,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,aAAa;YACb,UAAU;QACZ;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;4BAEC,SAAS,IAAM,eAAe,OAAO,EAAE;4BACvC,WAAW,AAAC,sIAKT,OAHC,kBAAkB,OAAO,EAAE,GACzB,sCACA,8EACH;;8CAGH,6LAAC;oCAAK,WAAU;8CAAiB,OAAO,IAAI;;;;;;8CAC5C,6LAAC;oCAAK,WAAU;8CAAiB,OAAO,KAAK;;;;;;gCAC5C,OAAO,QAAQ,kBACd,6LAAC;oCAAK,WAAU;8CAA8B,OAAO,QAAQ;;;;;;;2BAb1D,OAAO,EAAE;;;;;;;;;;8BAoBpB,6LAAC;oBAAI,WAAU;8BACZ,QAAQ,GAAG,CAAC,CAAC,SACZ,kBAAkB,OAAO,EAAE,kBACzB,6LAAC;4BAAoB,WAAU;;8CAC7B,6LAAC;oCAAK,WAAU;8CAAQ,OAAO,IAAI;;;;;;8CACnC,6LAAC;8CAAM,OAAO,WAAW;;;;;;gCACxB,OAAO,QAAQ,kBACd,6LAAC;oCAAK,WAAU;8CACb,OAAO,QAAQ;;;;;;;2BALZ,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;AAejC;KAjGgB", "debugId": null}}, {"offset": {"line": 11203, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { AlgorithmLibrary } from '@/components/algorithm-library';\nimport { AnalysisWorkspace } from '@/components/analysis-workspace';\nimport { Dashboard } from '@/components/dashboard';\nimport { DataManagement } from '@/components/data-management';\nimport { ScenarioPlanning } from '@/components/scenario-planning';\nimport { CapexOptimization } from '@/components/capex-optimization';\nimport { RevenueForecasting } from '@/components/revenue-forecasting';\nimport { InsightsReports } from '@/components/insights-reports';\nimport { AdvancedAnalytics } from '@/components/advanced-analytics';\nimport { StreamNavigation } from '@/components/stream-navigation';\nimport { apiClient } from '@/lib/api';\nimport type { AlgorithmsResponse } from '@/lib/api';\n\nexport default function Home() {\n  const [currentStream, setCurrentStream] = useState<'dashboard' | 'data' | 'scenarios' | 'capex' | 'revenue' | 'insights' | 'advanced'>('dashboard');\n  const [currentView, setCurrentView] = useState<'dashboard' | 'algorithms' | 'workspace' | 'data'>('dashboard');\n  const [algorithms, setAlgorithms] = useState<AlgorithmsResponse | null>(null);\n  const [selectedAlgorithm, setSelectedAlgorithm] = useState<any>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    loadAlgorithms();\n  }, []);\n\n  const loadAlgorithms = async () => {\n    try {\n      setIsLoading(true);\n      console.log('Loading algorithms from API...');\n      const data = await apiClient.getAlgorithms();\n      console.log('Algorithms loaded:', data);\n      setAlgorithms(data);\n      setError(null);\n    } catch (err) {\n      setError('Failed to load algorithms. Please check if the backend is running.');\n      console.error('Error loading algorithms:', err);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleAlgorithmSelect = (algorithm: any, category: string) => {\n    console.log('Algorithm selected:', algorithm, 'Category:', category);\n    setSelectedAlgorithm({ ...algorithm, category });\n    setCurrentView('workspace');\n    console.log('View changed to workspace');\n  };\n\n  const handleViewChange = (view: 'dashboard' | 'algorithms' | 'workspace' | 'data') => {\n    setCurrentView(view);\n  };\n\n  const handleStreamChange = (stream: 'dashboard' | 'data' | 'scenarios' | 'capex' | 'revenue' | 'insights' | 'advanced') => {\n    setCurrentStream(stream);\n    // Map streams to legacy views for backward compatibility\n    if (stream === 'data') {\n      setCurrentView('data');\n    } else {\n      setCurrentView('dashboard');\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Loading OneOptimizer...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-red-50 to-pink-100 flex items-center justify-center\">\n        <div className=\"text-center max-w-md mx-auto p-6\">\n          <div className=\"text-red-600 text-6xl mb-4\">⚠️</div>\n          <h1 className=\"text-2xl font-bold text-red-800 mb-2\">Connection Error</h1>\n          <p className=\"text-red-600 mb-4\">{error}</p>\n          <button\n            onClick={loadAlgorithms}\n            className=\"bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors\"\n          >\n            Retry Connection\n          </button>\n          <div className=\"mt-4 text-sm text-gray-600\">\n            <p>Make sure the FastAPI backend is running on port 8000:</p>\n            <code className=\"bg-gray-100 px-2 py-1 rounded mt-2 block\">\n              cd backend && python main.py\n            </code>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <h1 className=\"text-2xl font-bold text-gray-900\">\n                📊 OneOptimizer\n              </h1>\n              <span className=\"ml-2 text-sm text-gray-500\">v2.0 - Stream-Based UX</span>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Stream Navigation */}\n      <StreamNavigation currentStream={currentStream} onStreamChange={handleStreamChange} />\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Debug info */}\n        <div className=\"mb-4 p-2 bg-gray-100 rounded text-xs\">\n          Current Stream: {currentStream} | Current View: {currentView} | Selected Algorithm: {selectedAlgorithm ? selectedAlgorithm.name : 'None'}\n        </div>\n\n        {/* Stream-based Content */}\n        {currentStream === 'dashboard' && (\n          <Dashboard\n            algorithms={algorithms}\n            onAlgorithmSelect={handleAlgorithmSelect}\n            onViewChange={handleViewChange}\n          />\n        )}\n\n        {currentStream === 'data' && (\n          <DataManagement\n            onDataUpdate={(data) => console.log('Data updated:', data)}\n          />\n        )}\n\n        {currentStream === 'scenarios' && (\n          <ScenarioPlanning\n            onScenarioUpdate={(scenario) => console.log('Scenario updated:', scenario)}\n          />\n        )}\n\n        {currentStream === 'capex' && (\n          <CapexOptimization\n            onOptimizationUpdate={(data) => console.log('CAPEX optimization updated:', data)}\n          />\n        )}\n\n        {currentStream === 'revenue' && (\n          <RevenueForecasting\n            onForecastUpdate={(forecast) => console.log('Revenue forecast updated:', forecast)}\n          />\n        )}\n\n        {currentStream === 'insights' && (\n          <InsightsReports\n            onReportUpdate={(report) => console.log('Report updated:', report)}\n          />\n        )}\n\n        {currentStream === 'advanced' && (\n          <AdvancedAnalytics\n            onAnalysisUpdate={(analysis) => console.log('Advanced analysis updated:', analysis)}\n          />\n        )}\n\n        {/* Legacy Algorithm Views (for backward compatibility) */}\n        {currentView === 'algorithms' && algorithms && (\n          <AlgorithmLibrary\n            algorithms={algorithms}\n            onAlgorithmSelect={handleAlgorithmSelect}\n          />\n        )}\n\n        {currentView === 'workspace' && selectedAlgorithm && (\n          <AnalysisWorkspace\n            algorithm={selectedAlgorithm}\n            onBack={() => setCurrentView('algorithms')}\n          />\n        )}\n\n        {currentView === 'workspace' && !selectedAlgorithm && (\n          <div className=\"bg-white rounded-lg shadow-sm p-6 text-center\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">No Algorithm Selected</h3>\n            <p className=\"text-gray-600 mb-4\">Please select an algorithm from the library first.</p>\n            <button\n              onClick={() => setCurrentView('algorithms')}\n              className=\"bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors\"\n            >\n              Go to Algorithm Library\n            </button>\n          </div>\n        )}\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAbA;;;;;;;;;;;;;AAgBe,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsF;IACvI,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqD;IAClG,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;IACxE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAChE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR;QACF;yBAAG,EAAE;IAEL,MAAM,iBAAiB;QACrB,IAAI;YACF,aAAa;YACb,QAAQ,GAAG,CAAC;YACZ,MAAM,OAAO,MAAM,oHAAA,CAAA,YAAS,CAAC,aAAa;YAC1C,QAAQ,GAAG,CAAC,sBAAsB;YAClC,cAAc;YACd,SAAS;QACX,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,wBAAwB,CAAC,WAAgB;QAC7C,QAAQ,GAAG,CAAC,uBAAuB,WAAW,aAAa;QAC3D,qBAAqB;YAAE,GAAG,SAAS;YAAE;QAAS;QAC9C,eAAe;QACf,QAAQ,GAAG,CAAC;IACd;IAEA,MAAM,mBAAmB,CAAC;QACxB,eAAe;IACjB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,iBAAiB;QACjB,yDAAyD;QACzD,IAAI,WAAW,QAAQ;YACrB,eAAe;QACjB,OAAO;YACL,eAAe;QACjB;IACF;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAA6B;;;;;;kCAC5C,6LAAC;wBAAG,WAAU;kCAAuC;;;;;;kCACrD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;kCAGD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAE;;;;;;0CACH,6LAAC;gCAAK,WAAU;0CAA2C;;;;;;;;;;;;;;;;;;;;;;;IAOrE;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;8CAGjD,6LAAC;oCAAK,WAAU;8CAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOrD,6LAAC,6IAAA,CAAA,mBAAgB;gBAAC,eAAe;gBAAe,gBAAgB;;;;;;0BAGhE,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC;wBAAI,WAAU;;4BAAuC;4BACnC;4BAAc;4BAAkB;4BAAY;4BAAwB,oBAAoB,kBAAkB,IAAI,GAAG;;;;;;;oBAInI,kBAAkB,6BACjB,6LAAC,kIAAA,CAAA,YAAS;wBACR,YAAY;wBACZ,mBAAmB;wBACnB,cAAc;;;;;;oBAIjB,kBAAkB,wBACjB,6LAAC,2IAAA,CAAA,iBAAc;wBACb,cAAc,CAAC,OAAS,QAAQ,GAAG,CAAC,iBAAiB;;;;;;oBAIxD,kBAAkB,6BACjB,6LAAC,6IAAA,CAAA,mBAAgB;wBACf,kBAAkB,CAAC,WAAa,QAAQ,GAAG,CAAC,qBAAqB;;;;;;oBAIpE,kBAAkB,yBACjB,6LAAC,8IAAA,CAAA,oBAAiB;wBAChB,sBAAsB,CAAC,OAAS,QAAQ,GAAG,CAAC,+BAA+B;;;;;;oBAI9E,kBAAkB,2BACjB,6LAAC,+IAAA,CAAA,qBAAkB;wBACjB,kBAAkB,CAAC,WAAa,QAAQ,GAAG,CAAC,6BAA6B;;;;;;oBAI5E,kBAAkB,4BACjB,6LAAC,4IAAA,CAAA,kBAAe;wBACd,gBAAgB,CAAC,SAAW,QAAQ,GAAG,CAAC,mBAAmB;;;;;;oBAI9D,kBAAkB,4BACjB,6LAAC,8IAAA,CAAA,oBAAiB;wBAChB,kBAAkB,CAAC,WAAa,QAAQ,GAAG,CAAC,8BAA8B;;;;;;oBAK7E,gBAAgB,gBAAgB,4BAC/B,6LAAC,6IAAA,CAAA,mBAAgB;wBACf,YAAY;wBACZ,mBAAmB;;;;;;oBAItB,gBAAgB,eAAe,mCAC9B,6LAAC,8IAAA,CAAA,oBAAiB;wBAChB,WAAW;wBACX,QAAQ,IAAM,eAAe;;;;;;oBAIhC,gBAAgB,eAAe,CAAC,mCAC/B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAClC,6LAAC;gCACC,SAAS,IAAM,eAAe;gCAC9B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAzLwB;KAAA", "debugId": null}}]}